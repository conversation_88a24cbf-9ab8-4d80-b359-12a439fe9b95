package utils

import (
	"net/http"
)

// Response 统一响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// PageResponse 分页响应结构
type PageResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Total   int64       `json:"total"`
	Page    int         `json:"page"`
	Size    int         `json:"size"`
}

// SuccessResponse 成功响应
func SuccessResponse(data interface{}) Response {
	return Response{
		Code:    http.StatusOK,
		Message: "success",
		Data:    data,
	}
}

// SuccessResponseWithMessage 带消息的成功响应
func SuccessResponseWithMessage(message string, data interface{}) Response {
	return Response{
		Code:    http.StatusOK,
		Message: message,
		Data:    data,
	}
}

// ErrorResponse 错误响应
func ErrorResponse(code int, message string) Response {
	return Response{
		Code:    code,
		Message: message,
	}
}

// ErrorResponseWithData 带数据的错误响应
func ErrorResponseWithData(code int, message string, data interface{}) Response {
	return Response{
		Code:    code,
		Message: message,
		Data:    data,
	}
}

// PageSuccessResponse 分页成功响应
func PageSuccessResponse(data interface{}, total int64, page, size int) PageResponse {
	return PageResponse{
		Code:    http.StatusOK,
		Message: "success",
		Data:    data,
		Total:   total,
		Page:    page,
		Size:    size,
	}
}

// PageErrorResponse 分页错误响应
func PageErrorResponse(code int, message string) PageResponse {
	return PageResponse{
		Code:    code,
		Message: message,
		Total:   0,
		Page:    0,
		Size:    0,
	}
}

// 常用错误响应
var (
	// 4xx 客户端错误
	BadRequestResponse       = ErrorResponse(http.StatusBadRequest, "Bad Request")
	UnauthorizedResponse     = ErrorResponse(http.StatusUnauthorized, "Unauthorized")
	ForbiddenResponse        = ErrorResponse(http.StatusForbidden, "Forbidden")
	NotFoundResponse         = ErrorResponse(http.StatusNotFound, "Not Found")
	MethodNotAllowedResponse = ErrorResponse(http.StatusMethodNotAllowed, "Method Not Allowed")
	ConflictResponse         = ErrorResponse(http.StatusConflict, "Conflict")
	
	// 5xx 服务器错误
	InternalServerErrorResponse = ErrorResponse(http.StatusInternalServerError, "Internal Server Error")
	BadGatewayResponse          = ErrorResponse(http.StatusBadGateway, "Bad Gateway")
	ServiceUnavailableResponse  = ErrorResponse(http.StatusServiceUnavailable, "Service Unavailable")
)

// ValidationErrorResponse 验证错误响应
func ValidationErrorResponse(errors map[string]string) Response {
	return ErrorResponseWithData(http.StatusBadRequest, "Validation failed", errors)
}

// DatabaseErrorResponse 数据库错误响应
func DatabaseErrorResponse(err error) Response {
	return ErrorResponse(http.StatusInternalServerError, "Database error: "+err.Error())
}

// PermissionDeniedResponse 权限拒绝响应
func PermissionDeniedResponse() Response {
	return ErrorResponse(http.StatusForbidden, "Permission denied")
}

// ResourceNotFoundResponse 资源未找到响应
func ResourceNotFoundResponse(resource string) Response {
	return ErrorResponse(http.StatusNotFound, resource+" not found")
}

// DuplicateResourceResponse 资源重复响应
func DuplicateResourceResponse(resource string) Response {
	return ErrorResponse(http.StatusConflict, resource+" already exists")
}

// InvalidParameterResponse 无效参数响应
func InvalidParameterResponse(parameter string) Response {
	return ErrorResponse(http.StatusBadRequest, "Invalid parameter: "+parameter)
}

// MissingParameterResponse 缺少参数响应
func MissingParameterResponse(parameter string) Response {
	return ErrorResponse(http.StatusBadRequest, "Missing parameter: "+parameter)
}
