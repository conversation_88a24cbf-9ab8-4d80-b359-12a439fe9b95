#!/bin/bash

# 支付系统测试脚本
# 使用方法: ./test_payments.sh [environment] [base_url] [token]

set -e

# 默认配置
ENVIRONMENT=${1:-"sandbox"}
BASE_URL=${2:-"http://localhost:8080"}
TOKEN=${3:-"your_auth_token"}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# API请求函数
api_request() {
    local method=$1
    local endpoint=$2
    local data=$3
    
    if [ -n "$data" ]; then
        curl -s -X $method \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $TOKEN" \
            -d "$data" \
            "$BASE_URL$endpoint"
    else
        curl -s -X $method \
            -H "Authorization: Bearer $TOKEN" \
            "$BASE_URL$endpoint"
    fi
}

# 检查API响应
check_response() {
    local response=$1
    local test_name=$2
    
    if echo "$response" | jq -e '.code == 200' > /dev/null 2>&1; then
        log_success "$test_name - 测试通过"
        return 0
    else
        log_error "$test_name - 测试失败"
        echo "$response" | jq '.' 2>/dev/null || echo "$response"
        return 1
    fi
}

# 主函数
main() {
    log_info "开始支付系统测试"
    log_info "环境: $ENVIRONMENT"
    log_info "服务地址: $BASE_URL"
    echo ""

    # 1. 测试获取支付配置
    log_info "1. 测试获取支付配置..."
    response=$(api_request "GET" "/api/v1/payment/test/configs")
    if check_response "$response" "获取支付配置"; then
        echo "$response" | jq '.data[] | {pay_type, pay_type_name, environment, status}' 2>/dev/null
    fi
    echo ""

    # 2. 测试所有支付方式
    log_info "2. 测试所有支付方式..."
    response=$(api_request "GET" "/api/v1/payment/test/all?environment=$ENVIRONMENT&test_amount=0.01")
    if check_response "$response" "测试所有支付方式"; then
        echo "$response" | jq '.data[] | {pay_type_name, test_type, success, message, duration}' 2>/dev/null
    fi
    echo ""

    # 3. 测试微信支付
    log_info "3. 测试微信支付..."
    test_data='{
        "pay_type": 1,
        "environment": "'$ENVIRONMENT'",
        "test_amount": 0.01,
        "user_id": 999999,
        "product_id": 1
    }'
    response=$(api_request "POST" "/api/v1/payment/test/single" "$test_data")
    check_response "$response" "微信支付测试"
    echo ""

    # 4. 测试支付宝支付
    log_info "4. 测试支付宝支付..."
    test_data='{
        "pay_type": 2,
        "environment": "'$ENVIRONMENT'",
        "test_amount": 0.01,
        "user_id": 999999,
        "product_id": 1
    }'
    response=$(api_request "POST" "/api/v1/payment/test/single" "$test_data")
    check_response "$response" "支付宝支付测试"
    echo ""

    # 5. 测试抖音支付
    log_info "5. 测试抖音支付..."
    test_data='{
        "pay_type": 4,
        "environment": "'$ENVIRONMENT'",
        "test_amount": 0.01,
        "user_id": 999999,
        "product_id": 1
    }'
    response=$(api_request "POST" "/api/v1/payment/test/single" "$test_data")
    check_response "$response" "抖音支付测试"
    echo ""

    # 6. 测试苹果内购
    log_info "6. 测试苹果内购..."
    test_data='{
        "pay_type": 3,
        "environment": "'$ENVIRONMENT'",
        "test_amount": 0.01,
        "user_id": 999999,
        "product_id": 1
    }'
    response=$(api_request "POST" "/api/v1/payment/test/single" "$test_data")
    check_response "$response" "苹果内购测试"
    echo ""

    # 7. 生成测试报告
    log_info "7. 生成测试报告..."
    response=$(api_request "GET" "/api/v1/payment/test/report?environment=$ENVIRONMENT")
    if check_response "$response" "生成测试报告"; then
        echo "$response" | jq -r '.data.report' 2>/dev/null > "payment_test_report_$(date +%Y%m%d_%H%M%S).md"
        log_success "测试报告已保存到 payment_test_report_$(date +%Y%m%d_%H%M%S).md"
    fi
    echo ""

    log_info "支付系统测试完成"
}

# 小额支付测试函数
test_small_amount() {
    local pay_type=$1
    local amount=${2:-0.01}
    
    log_info "开始小额支付测试 - 支付类型: $pay_type, 金额: $amount"
    
    response=$(api_request "GET" "/api/v1/payment/test/small-amount?pay_type=$pay_type&amount=$amount")
    if check_response "$response" "小额支付测试"; then
        echo "$response" | jq '.data | {pay_type_name, success, message, duration}' 2>/dev/null
    fi
}

# 沙箱环境测试函数
test_sandbox() {
    log_info "开始沙箱环境测试..."
    ENVIRONMENT="sandbox"
    main
}

# 生产环境小额测试函数
test_production_small() {
    log_warning "开始生产环境小额测试..."
    log_warning "注意: 这将在生产环境进行真实支付测试!"
    
    read -p "确认继续? (y/N): " confirm
    if [[ $confirm != [yY] ]]; then
        log_info "测试已取消"
        exit 0
    fi
    
    # 测试各支付方式的小额支付
    test_small_amount 1 0.01  # 微信支付 1分钱
    test_small_amount 2 0.01  # 支付宝支付 1分钱
    test_small_amount 4 0.01  # 抖音支付 1分钱
}

# 检查依赖
check_dependencies() {
    if ! command -v curl &> /dev/null; then
        log_error "curl 未安装，请先安装 curl"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_error "jq 未安装，请先安装 jq"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "支付系统测试脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [environment] [base_url] [token]"
    echo ""
    echo "参数:"
    echo "  environment  测试环境 (sandbox/prod, 默认: sandbox)"
    echo "  base_url     API基础地址 (默认: http://localhost:8080)"
    echo "  token        认证令牌"
    echo ""
    echo "示例:"
    echo "  $0 sandbox http://localhost:8080 your_token"
    echo "  $0 prod https://api.yourdomain.com your_token"
    echo ""
    echo "特殊命令:"
    echo "  $0 sandbox-test    # 沙箱环境完整测试"
    echo "  $0 prod-small      # 生产环境小额测试"
    echo "  $0 help           # 显示帮助信息"
}

# 主程序入口
case "$1" in
    "help"|"-h"|"--help")
        show_help
        exit 0
        ;;
    "sandbox-test")
        check_dependencies
        test_sandbox
        ;;
    "prod-small")
        check_dependencies
        test_production_small
        ;;
    *)
        check_dependencies
        main
        ;;
esac
