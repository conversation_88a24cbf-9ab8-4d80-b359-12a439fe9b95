version: '3.8'

services:
  # 应用服务
  kakabs-admin:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: kakabs-admin
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - KAKABS_APP_MODE=prod
      - KAKABS_DATABASE_DEFAULT_HOST=mysql
      - KAKABS_DATABASE_ADMIN_HOST=mysql
      - KAKABS_DATABASE_CONFIG_HOST=mysql
      - KAKABS_DATABASE_WARBAND_HOST=mysql
      - KAKABS_REDIS_HOST=redis
    volumes:
      - ./conf:/app/conf:ro
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    depends_on:
      - mysql
      - redis
    networks:
      - kakabs-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: kakabs-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: match_center
      MYSQL_USER: xy_game001
      MYSQL_PASSWORD: gamepassword
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./dbsql:/docker-entrypoint-initdb.d:ro
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - kakabs-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: kakabs-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - kakabs-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: kakabs-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - kakabs-admin
    networks:
      - kakabs-network

  # 监控服务 - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: kakabs-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - kakabs-network

  # 监控服务 - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: kakabs-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - kakabs-network

# 数据卷
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

# 网络
networks:
  kakabs-network:
    driver: bridge
