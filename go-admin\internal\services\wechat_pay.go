package services

import (
	"crypto/md5"
	"encoding/xml"
	"fmt"
	"io"
	"kakabs-admin/internal/logger"
	"kakabs-admin/internal/models"
	"math/rand"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
)

// WechatPayService 微信支付服务
type WechatPayService struct {
	AppID     string
	MchID     string
	APIKey    string
	NotifyURL string
}

// NewWechatPayService 创建微信支付服务实例
func NewWechatPayService(config *models.PaymentConfig) *WechatPayService {
	return &WechatPayService{
		AppID:     config.AppID,
		MchID:     config.MchID,
		APIKey:    config.APIKey,
		NotifyURL: config.NotifyURL,
	}
}

// UnifiedOrderRequest 统一下单请求结构
type UnifiedOrderRequest struct {
	AppID          string `xml:"appid"`
	MchID          string `xml:"mch_id"`
	NonceStr       string `xml:"nonce_str"`
	Sign           string `xml:"sign"`
	Body           string `xml:"body"`
	OutTradeNo     string `xml:"out_trade_no"`
	TotalFee       int    `xml:"total_fee"`
	SpbillCreateIP string `xml:"spbill_create_ip"`
	NotifyURL      string `xml:"notify_url"`
	TradeType      string `xml:"trade_type"`
	OpenID         string `xml:"openid,omitempty"`
	SceneInfo      string `xml:"scene_info,omitempty"`
}

// UnifiedOrderResponse 统一下单响应结构
type UnifiedOrderResponse struct {
	ReturnCode string `xml:"return_code"`
	ReturnMsg  string `xml:"return_msg"`
	AppID      string `xml:"appid"`
	MchID      string `xml:"mch_id"`
	NonceStr   string `xml:"nonce_str"`
	Sign       string `xml:"sign"`
	ResultCode string `xml:"result_code"`
	PrepayID   string `xml:"prepay_id"`
	TradeType  string `xml:"trade_type"`
	MwebURL    string `xml:"mweb_url"`
	ErrCode    string `xml:"err_code"`
	ErrCodeDes string `xml:"err_code_des"`
}

// PayNotifyData 支付回调数据结构
type PayNotifyData struct {
	ReturnCode    string `xml:"return_code"`
	ReturnMsg     string `xml:"return_msg"`
	AppID         string `xml:"appid"`
	MchID         string `xml:"mch_id"`
	NonceStr      string `xml:"nonce_str"`
	Sign          string `xml:"sign"`
	ResultCode    string `xml:"result_code"`
	OutTradeNo    string `xml:"out_trade_no"`
	TransactionID string `xml:"transaction_id"`
	TotalFee      int    `xml:"total_fee"`
	TimeEnd       string `xml:"time_end"`
}

// CreateOrder 创建支付订单
func (w *WechatPayService) CreateOrder(order *models.PaymentOrder, product *models.PaymentProduct) (*models.WechatPayResponse, error) {
	// 生成随机字符串
	nonceStr := w.generateNonceStr()
	
	// 构建统一下单请求
	req := &UnifiedOrderRequest{
		AppID:          w.AppID,
		MchID:          w.MchID,
		NonceStr:       nonceStr,
		Body:           product.Name,
		OutTradeNo:     order.OutTradeNo,
		TotalFee:       order.TotalFee,
		SpbillCreateIP: order.ClientIP,
		NotifyURL:      w.NotifyURL,
		TradeType:      w.getTradeType(order.PayMethod),
	}

	// 根据支付方式设置特殊参数
	switch order.PayMethod {
	case 2: // H5支付
		req.SceneInfo = `{"h5_info":{"type":"Wap","wap_url":"https://admin.kakabs.com","wap_name":"卡卡棋牌"}}`
	case 3, 4: // 小程序支付或公众号支付
		// 这里需要传入用户的openid，应该从请求中获取
		// req.OpenID = openid
	}

	// 生成签名
	req.Sign = w.generateSign(req)

	// 发送统一下单请求
	resp, err := w.unifiedOrder(req)
	if err != nil {
		return nil, fmt.Errorf("unified order failed: %w", err)
	}

	if resp.ReturnCode != "SUCCESS" {
		return nil, fmt.Errorf("unified order return failed: %s", resp.ReturnMsg)
	}

	if resp.ResultCode != "SUCCESS" {
		return nil, fmt.Errorf("unified order result failed: %s - %s", resp.ErrCode, resp.ErrCodeDes)
	}

	// 构建支付参数
	payData := w.buildPayData(resp, order.PayMethod)

	return &models.WechatPayResponse{
		OrderID:    order.OrderID,
		OutTradeNo: order.OutTradeNo,
		PayData:    payData,
	}, nil
}

// unifiedOrder 发送统一下单请求
func (w *WechatPayService) unifiedOrder(req *UnifiedOrderRequest) (*UnifiedOrderResponse, error) {
	// 将请求结构体转换为XML
	xmlData, err := xml.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("marshal request failed: %w", err)
	}

	// 添加XML头
	xmlStr := `<?xml version="1.0" encoding="UTF-8"?><xml>` + string(xmlData)[5:len(xmlData)-6] + `</xml>`

	// 发送HTTP请求
	resp, err := http.Post("https://api.mch.weixin.qq.com/pay/unifiedorder", "application/xml", strings.NewReader(xmlStr))
	if err != nil {
		return nil, fmt.Errorf("http request failed: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response failed: %w", err)
	}

	// 解析响应XML
	var response UnifiedOrderResponse
	err = xml.Unmarshal(body, &response)
	if err != nil {
		return nil, fmt.Errorf("unmarshal response failed: %w", err)
	}

	logger.Debug("Wechat unified order response", zap.String("response", string(body)))

	return &response, nil
}

// buildPayData 构建支付参数
func (w *WechatPayService) buildPayData(resp *UnifiedOrderResponse, payMethod int) map[string]interface{} {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	nonceStr := w.generateNonceStr()

	switch payMethod {
	case 1: // APP支付
		payData := map[string]interface{}{
			"appid":     w.AppID,
			"partnerid": w.MchID,
			"prepayid":  resp.PrepayID,
			"package":   "Sign=WXPay",
			"noncestr":  nonceStr,
			"timestamp": timestamp,
		}
		
		// 生成APP支付签名
		sign := w.generateAppPaySign(payData)
		payData["sign"] = sign
		
		return payData

	case 2: // H5支付
		return map[string]interface{}{
			"mweb_url": resp.MwebURL,
		}

	case 3: // 小程序支付
		payData := map[string]interface{}{
			"appId":     w.AppID,
			"timeStamp": timestamp,
			"nonceStr":  nonceStr,
			"package":   "prepay_id=" + resp.PrepayID,
			"signType":  "MD5",
		}
		
		// 生成小程序支付签名
		sign := w.generateMiniPaySign(payData)
		payData["paySign"] = sign
		
		return payData

	case 4: // 公众号支付
		payData := map[string]interface{}{
			"appId":     w.AppID,
			"timeStamp": timestamp,
			"nonceStr":  nonceStr,
			"package":   "prepay_id=" + resp.PrepayID,
			"signType":  "MD5",
		}
		
		// 生成公众号支付签名
		sign := w.generateJSAPIPaySign(payData)
		payData["paySign"] = sign
		
		return payData

	default:
		return map[string]interface{}{}
	}
}

// VerifyNotify 验证支付回调
func (w *WechatPayService) VerifyNotify(xmlData []byte) (*PayNotifyData, error) {
	var notify PayNotifyData
	err := xml.Unmarshal(xmlData, &notify)
	if err != nil {
		return nil, fmt.Errorf("unmarshal notify data failed: %w", err)
	}

	// 验证签名
	if !w.verifyNotifySign(&notify) {
		return nil, fmt.Errorf("verify notify sign failed")
	}

	return &notify, nil
}

// generateNonceStr 生成随机字符串
func (w *WechatPayService) generateNonceStr() string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, 32)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// generateSign 生成签名
func (w *WechatPayService) generateSign(req *UnifiedOrderRequest) string {
	// 构建签名参数
	params := map[string]string{
		"appid":            req.AppID,
		"mch_id":           req.MchID,
		"nonce_str":        req.NonceStr,
		"body":             req.Body,
		"out_trade_no":     req.OutTradeNo,
		"total_fee":        strconv.Itoa(req.TotalFee),
		"spbill_create_ip": req.SpbillCreateIP,
		"notify_url":       req.NotifyURL,
		"trade_type":       req.TradeType,
	}

	if req.OpenID != "" {
		params["openid"] = req.OpenID
	}
	if req.SceneInfo != "" {
		params["scene_info"] = req.SceneInfo
	}

	return w.signParams(params)
}

// signParams 对参数进行签名
func (w *WechatPayService) signParams(params map[string]string) string {
	// 排序参数
	var keys []string
	for k := range params {
		if params[k] != "" {
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	// 构建签名字符串
	var signStr strings.Builder
	for i, k := range keys {
		if i > 0 {
			signStr.WriteString("&")
		}
		signStr.WriteString(k)
		signStr.WriteString("=")
		signStr.WriteString(params[k])
	}
	signStr.WriteString("&key=")
	signStr.WriteString(w.APIKey)

	// MD5签名
	hash := md5.Sum([]byte(signStr.String()))
	return strings.ToUpper(fmt.Sprintf("%x", hash))
}

// getTradeType 获取交易类型
func (w *WechatPayService) getTradeType(payMethod int) string {
	switch payMethod {
	case 1:
		return "APP"
	case 2:
		return "MWEB"
	case 3, 4:
		return "JSAPI"
	default:
		return "APP"
	}
}

// generateAppPaySign 生成APP支付签名
func (w *WechatPayService) generateAppPaySign(payData map[string]interface{}) string {
	params := map[string]string{
		"appid":     payData["appid"].(string),
		"partnerid": payData["partnerid"].(string),
		"prepayid":  payData["prepayid"].(string),
		"package":   payData["package"].(string),
		"noncestr":  payData["noncestr"].(string),
		"timestamp": payData["timestamp"].(string),
	}
	return w.signParams(params)
}

// generateMiniPaySign 生成小程序支付签名
func (w *WechatPayService) generateMiniPaySign(payData map[string]interface{}) string {
	params := map[string]string{
		"appId":     payData["appId"].(string),
		"timeStamp": payData["timeStamp"].(string),
		"nonceStr":  payData["nonceStr"].(string),
		"package":   payData["package"].(string),
		"signType":  payData["signType"].(string),
	}
	return w.signParams(params)
}

// generateJSAPIPaySign 生成公众号支付签名
func (w *WechatPayService) generateJSAPIPaySign(payData map[string]interface{}) string {
	return w.generateMiniPaySign(payData) // 与小程序支付签名方式相同
}

// verifyNotifySign 验证回调签名
func (w *WechatPayService) verifyNotifySign(notify *PayNotifyData) bool {
	params := map[string]string{
		"return_code":    notify.ReturnCode,
		"return_msg":     notify.ReturnMsg,
		"appid":          notify.AppID,
		"mch_id":         notify.MchID,
		"nonce_str":      notify.NonceStr,
		"result_code":    notify.ResultCode,
		"out_trade_no":   notify.OutTradeNo,
		"transaction_id": notify.TransactionID,
		"total_fee":      strconv.Itoa(notify.TotalFee),
		"time_end":       notify.TimeEnd,
	}

	expectedSign := w.signParams(params)
	return expectedSign == notify.Sign
}
