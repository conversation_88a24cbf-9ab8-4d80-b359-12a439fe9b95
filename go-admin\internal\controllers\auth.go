package controllers

import (
	"crypto/md5"
	"fmt"
	"kakabs-admin/internal/database"
	"kakabs-admin/internal/logger"
	"kakabs-admin/internal/models"
	"kakabs-admin/internal/utils"
	"net/http"

	"github.com/beego/beego/v2/server/web"
	"go.uber.org/zap"
)

// AuthController 认证控制器
type AuthController struct {
	web.Controller
}

// LoginRequest 登录请求结构
type LoginRequest struct {
	AdminName string `json:"admin_name" valid:"Required"`
	Password  string `json:"password" valid:"Required"`
}

// LoginResponse 登录响应结构
type LoginResponse struct {
	Token     string      `json:"token"`
	AdminInfo models.Admin `json:"admin_info"`
}

// Login 管理员登录
// @Title 管理员登录
// @Description 管理员登录接口
// @Param body body LoginRequest true "登录信息"
// @Success 200 {object} utils.Response{data=LoginResponse}
// @Failure 400 {object} utils.Response
// @Failure 401 {object} utils.Response
// @router /login [post]
func (c *AuthController) Login() {
	var req LoginRequest
	if err := c.ParseForm(&req); err != nil {
		logger.Error("Failed to parse login request", zap.Error(err))
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid request format")
		c.ServeJSON()
		return
	}

	// 验证必填字段
	if req.AdminName == "" || req.Password == "" {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Admin name and password are required")
		c.ServeJSON()
		return
	}

	// 查询管理员信息
	o := database.GetAdminOrm()
	admin := models.Admin{}
	
	err := o.QueryTable("admin").Filter("admin_name", req.AdminName).One(&admin)
	if err != nil {
		logger.Warn("Admin not found", zap.String("admin_name", req.AdminName), zap.Error(err))
		c.Data["json"] = utils.ErrorResponse(http.StatusUnauthorized, "Invalid credentials")
		c.ServeJSON()
		return
	}

	// 验证密码 (MD5加密)
	hashedPassword := fmt.Sprintf("%x", md5.Sum([]byte(req.Password)))
	if admin.AdminPassword != hashedPassword {
		logger.Warn("Invalid password", zap.String("admin_name", req.AdminName))
		c.Data["json"] = utils.ErrorResponse(http.StatusUnauthorized, "Invalid credentials")
		c.ServeJSON()
		return
	}

	// 生成JWT token
	token, err := utils.GenerateJWT(admin.ID, admin.AdminName, admin.AdminType, admin.ChannelID)
	if err != nil {
		logger.Error("Failed to generate JWT token", zap.Error(err))
		c.Data["json"] = utils.ErrorResponse(http.StatusInternalServerError, "Failed to generate token")
		c.ServeJSON()
		return
	}

	// 清除密码字段
	admin.AdminPassword = ""

	logger.Info("Admin login successful",
		zap.String("admin_name", admin.AdminName),
		zap.Int("admin_type", admin.AdminType),
		zap.String("ip", c.Ctx.Input.IP()),
	)

	c.Data["json"] = utils.SuccessResponse(LoginResponse{
		Token:     token,
		AdminInfo: admin,
	})
	c.ServeJSON()
}

// Logout 管理员登出
// @Title 管理员登出
// @Description 管理员登出接口
// @Success 200 {object} utils.Response
// @Security ApiKeyAuth
// @router /logout [post]
func (c *AuthController) Logout() {
	// 从上下文获取用户信息
	userID := c.Ctx.Input.GetData("user_id")
	adminName := c.Ctx.Input.GetData("admin_name")

	logger.Info("Admin logout",
		zap.Any("user_id", userID),
		zap.Any("admin_name", adminName),
		zap.String("ip", c.Ctx.Input.IP()),
	)

	// TODO: 可以在这里实现token黑名单机制
	// 将token加入Redis黑名单，防止被继续使用

	c.Data["json"] = utils.SuccessResponse(nil)
	c.ServeJSON()
}

// RefreshToken 刷新token
// @Title 刷新token
// @Description 刷新JWT token
// @Success 200 {object} utils.Response{data=string}
// @Failure 400 {object} utils.Response
// @Security ApiKeyAuth
// @router /refresh [post]
func (c *AuthController) RefreshToken() {
	// 获取当前token
	authHeader := c.Ctx.Input.Header("Authorization")
	if authHeader == "" {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Missing authorization header")
		c.ServeJSON()
		return
	}

	token := authHeader[7:] // 去掉 "Bearer " 前缀
	
	// 刷新token
	newToken, err := utils.RefreshJWT(token)
	if err != nil {
		logger.Error("Failed to refresh token", zap.Error(err))
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Failed to refresh token")
		c.ServeJSON()
		return
	}

	c.Data["json"] = utils.SuccessResponse(map[string]string{
		"token": newToken,
	})
	c.ServeJSON()
}

// Profile 获取当前用户信息
// @Title 获取当前用户信息
// @Description 获取当前登录用户的详细信息
// @Success 200 {object} utils.Response{data=models.Admin}
// @Failure 401 {object} utils.Response
// @Security ApiKeyAuth
// @router /profile [get]
func (c *AuthController) Profile() {
	// 从上下文获取用户ID
	userID := c.Ctx.Input.GetData("user_id")
	if userID == nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusUnauthorized, "User not authenticated")
		c.ServeJSON()
		return
	}

	// 查询管理员详细信息
	o := database.GetAdminOrm()
	admin := models.Admin{}
	
	err := o.QueryTable("admin").Filter("id", userID).One(&admin)
	if err != nil {
		logger.Error("Failed to get admin profile", zap.Any("user_id", userID), zap.Error(err))
		c.Data["json"] = utils.ErrorResponse(http.StatusNotFound, "Admin not found")
		c.ServeJSON()
		return
	}

	// 清除密码字段
	admin.AdminPassword = ""

	c.Data["json"] = utils.SuccessResponse(admin)
	c.ServeJSON()
}

// ChangePassword 修改密码
// @Title 修改密码
// @Description 修改当前用户密码
// @Param body body ChangePasswordRequest true "密码信息"
// @Success 200 {object} utils.Response
// @Failure 400 {object} utils.Response
// @Security ApiKeyAuth
// @router /change-password [post]
func (c *AuthController) ChangePassword() {
	type ChangePasswordRequest struct {
		OldPassword string `json:"old_password" valid:"Required"`
		NewPassword string `json:"new_password" valid:"Required;MinSize(6)"`
	}

	var req ChangePasswordRequest
	if err := c.ParseForm(&req); err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid request format")
		c.ServeJSON()
		return
	}

	// 从上下文获取用户ID
	userID := c.Ctx.Input.GetData("user_id")
	if userID == nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusUnauthorized, "User not authenticated")
		c.ServeJSON()
		return
	}

	// 查询当前用户信息
	o := database.GetAdminOrm()
	admin := models.Admin{}
	
	err := o.QueryTable("admin").Filter("id", userID).One(&admin)
	if err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusNotFound, "Admin not found")
		c.ServeJSON()
		return
	}

	// 验证旧密码
	oldHashedPassword := fmt.Sprintf("%x", md5.Sum([]byte(req.OldPassword)))
	if admin.AdminPassword != oldHashedPassword {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid old password")
		c.ServeJSON()
		return
	}

	// 更新密码
	newHashedPassword := fmt.Sprintf("%x", md5.Sum([]byte(req.NewPassword)))
	admin.AdminPassword = newHashedPassword
	
	if _, err := o.Update(&admin, "admin_password"); err != nil {
		logger.Error("Failed to update password", zap.Any("user_id", userID), zap.Error(err))
		c.Data["json"] = utils.ErrorResponse(http.StatusInternalServerError, "Failed to update password")
		c.ServeJSON()
		return
	}

	logger.Info("Password changed successfully", zap.Any("user_id", userID))
	c.Data["json"] = utils.SuccessResponse(nil)
	c.ServeJSON()
}
