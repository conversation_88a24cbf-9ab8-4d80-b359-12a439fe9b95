package controllers

import (
	"fmt"
	"kakabs-admin/internal/database"
	"kakabs-admin/internal/logger"
	"kakabs-admin/internal/models"
	"kakabs-admin/internal/utils"
	"net/http"
	"strconv"
	"time"

	"github.com/beego/beego/v2/server/web"
	"go.uber.org/zap"
)

// GameController 游戏管理控制器
type GameController struct {
	web.Controller
}

// GetMatches 获取比赛列表
// @Title 获取比赛列表
// @Description 分页获取比赛列表
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(20)
// @Param match_name query string false "比赛名称"
// @Param game_type query string false "游戏类型"
// @Param over_state query int false "比赛状态"
// @Success 200 {object} utils.PageResponse{data=[]models.Match}
// @Security ApiKeyAuth
// @router /matches [get]
func (c *GameController) GetMatches() {
	var req models.MatchQuery
	if err := c.ParseForm(&req); err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid request parameters")
		c.ServeJSON()
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 20
	}

	o := database.GetOrm()
	qs := o.QueryTable("matches")

	// 构建查询条件
	if req.MatchName != "" {
		qs = qs.Filter("match_name__icontains", req.MatchName)
	}
	if req.GameType != "" {
		qs = qs.Filter("game_type", req.GameType)
	}
	if req.OverState >= 0 {
		qs = qs.Filter("over_state", req.OverState)
	}
	if req.StartDate != "" {
		qs = qs.Filter("start_time__gte", req.StartDate)
	}
	if req.EndDate != "" {
		qs = qs.Filter("end_time__lte", req.EndDate)
	}

	// 获取总数
	total, err := qs.Count()
	if err != nil {
		logger.Error("Failed to count matches", zap.Error(err))
		c.Data["json"] = utils.DatabaseErrorResponse(err)
		c.ServeJSON()
		return
	}

	// 分页查询
	var matches []models.Match
	offset := (req.Page - 1) * req.Size
	_, err = qs.OrderBy("-create_time").Limit(req.Size, offset).All(&matches)
	if err != nil {
		logger.Error("Failed to get matches", zap.Error(err))
		c.Data["json"] = utils.DatabaseErrorResponse(err)
		c.ServeJSON()
		return
	}

	c.Data["json"] = utils.PageSuccessResponse(matches, total, req.Page, req.Size)
	c.ServeJSON()
}

// GetMatch 获取比赛详情
// @Title 获取比赛详情
// @Description 根据ID获取比赛详细信息
// @Param id path string true "比赛ID"
// @Success 200 {object} utils.Response{data=models.Match}
// @Failure 404 {object} utils.Response
// @Security ApiKeyAuth
// @router /matches/:id [get]
func (c *GameController) GetMatch() {
	matchID := c.Ctx.Input.Param(":id")
	if matchID == "" {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Match ID is required")
		c.ServeJSON()
		return
	}

	o := database.GetOrm()
	match := models.Match{}

	err := o.QueryTable("matches").Filter("match_id", matchID).One(&match)
	if err != nil {
		logger.Error("Failed to get match", zap.String("match_id", matchID), zap.Error(err))
		c.Data["json"] = utils.ResourceNotFoundResponse("Match")
		c.ServeJSON()
		return
	}

	c.Data["json"] = utils.SuccessResponse(match)
	c.ServeJSON()
}

// CreateMatch 创建比赛
// @Title 创建比赛
// @Description 创建新的比赛
// @Param body body models.MatchRequest true "比赛信息"
// @Success 200 {object} utils.Response{data=models.Match}
// @Security ApiKeyAuth
// @router /matches [post]
func (c *GameController) CreateMatch() {
	var req models.MatchRequest
	if err := c.ParseForm(&req); err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid request format")
		c.ServeJSON()
		return
	}

	// 生成比赛ID
	matchID := c.generateMatchID()

	// 创建比赛
	match := models.Match{
		MatchID:   matchID,
		MatchName: req.MatchName,
		GameType:  req.GameType,
		GameName:  req.GameName,
		MatchType: req.MatchType,
		PlayerMin: req.PlayerMin,
		PlayerMax: req.PlayerMax,
		CostVas:   req.CostVas,
		AwardDes:  req.AwardDes,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		OverState: 0, // 未开始
		OpenType:  req.OpenType,
		IsSelf:    0, // 官方赛事
	}

	o := database.GetOrm()
	_, err := o.Insert(&match)
	if err != nil {
		logger.Error("Failed to create match", zap.Error(err))
		c.Data["json"] = utils.ErrorResponse(http.StatusInternalServerError, "Failed to create match")
		c.ServeJSON()
		return
	}

	logger.Info("Match created successfully",
		zap.String("match_id", matchID),
		zap.String("match_name", req.MatchName),
		zap.String("game_type", req.GameType),
	)

	c.Data["json"] = utils.SuccessResponse(match)
	c.ServeJSON()
}

// UpdateMatch 更新比赛
// @Title 更新比赛
// @Description 更新比赛信息
// @Param id path string true "比赛ID"
// @Param body body models.MatchRequest true "比赛信息"
// @Success 200 {object} utils.Response
// @Security ApiKeyAuth
// @router /matches/:id [put]
func (c *GameController) UpdateMatch() {
	matchID := c.Ctx.Input.Param(":id")
	if matchID == "" {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Match ID is required")
		c.ServeJSON()
		return
	}

	var req models.MatchRequest
	if err := c.ParseForm(&req); err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid request format")
		c.ServeJSON()
		return
	}

	o := database.GetOrm()

	// 查找比赛
	var match models.Match
	err := o.QueryTable("matches").Filter("match_id", matchID).One(&match)
	if err != nil {
		logger.Error("Failed to get match", zap.String("match_id", matchID), zap.Error(err))
		c.Data["json"] = utils.ResourceNotFoundResponse("Match")
		c.ServeJSON()
		return
	}

	// 更新比赛信息
	match.MatchName = req.MatchName
	match.GameType = req.GameType
	match.GameName = req.GameName
	match.MatchType = req.MatchType
	match.PlayerMin = req.PlayerMin
	match.PlayerMax = req.PlayerMax
	match.CostVas = req.CostVas
	match.AwardDes = req.AwardDes
	match.StartTime = req.StartTime
	match.EndTime = req.EndTime
	match.OpenType = req.OpenType

	_, err = o.Update(&match)
	if err != nil {
		logger.Error("Failed to update match", zap.String("match_id", matchID), zap.Error(err))
		c.Data["json"] = utils.ErrorResponse(http.StatusInternalServerError, "Failed to update match")
		c.ServeJSON()
		return
	}

	logger.Info("Match updated successfully", zap.String("match_id", matchID))
	c.Data["json"] = utils.SuccessResponse(nil)
	c.ServeJSON()
}

// DeleteMatch 删除比赛
// @Title 删除比赛
// @Description 删除指定比赛
// @Param id path string true "比赛ID"
// @Success 200 {object} utils.Response
// @Security ApiKeyAuth
// @router /matches/:id [delete]
func (c *GameController) DeleteMatch() {
	matchID := c.Ctx.Input.Param(":id")
	if matchID == "" {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Match ID is required")
		c.ServeJSON()
		return
	}

	o := database.GetOrm()

	// 检查比赛是否存在
	var match models.Match
	err := o.QueryTable("matches").Filter("match_id", matchID).One(&match)
	if err != nil {
		logger.Error("Failed to get match", zap.String("match_id", matchID), zap.Error(err))
		c.Data["json"] = utils.ResourceNotFoundResponse("Match")
		c.ServeJSON()
		return
	}

	// 检查比赛状态
	if match.OverState == 1 {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Cannot delete running match")
		c.ServeJSON()
		return
	}

	// 删除比赛
	_, err = o.QueryTable("matches").Filter("match_id", matchID).Delete()
	if err != nil {
		logger.Error("Failed to delete match", zap.String("match_id", matchID), zap.Error(err))
		c.Data["json"] = utils.ErrorResponse(http.StatusInternalServerError, "Failed to delete match")
		c.ServeJSON()
		return
	}

	logger.Info("Match deleted successfully", zap.String("match_id", matchID))
	c.Data["json"] = utils.SuccessResponse(nil)
	c.ServeJSON()
}

// StartMatch 开始比赛
// @Title 开始比赛
// @Description 手动开始比赛
// @Param id path string true "比赛ID"
// @Success 200 {object} utils.Response
// @Security ApiKeyAuth
// @router /matches/:id/start [post]
func (c *GameController) StartMatch() {
	matchID := c.Ctx.Input.Param(":id")
	if matchID == "" {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Match ID is required")
		c.ServeJSON()
		return
	}

	o := database.GetOrm()

	// 查找比赛
	var match models.Match
	err := o.QueryTable("matches").Filter("match_id", matchID).One(&match)
	if err != nil {
		logger.Error("Failed to get match", zap.String("match_id", matchID), zap.Error(err))
		c.Data["json"] = utils.ResourceNotFoundResponse("Match")
		c.ServeJSON()
		return
	}

	// 检查比赛状态
	if match.OverState != 0 {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Match is not in waiting state")
		c.ServeJSON()
		return
	}

	// 更新比赛状态
	match.OverState = 1 // 进行中
	match.StartTime = time.Now()

	_, err = o.Update(&match, "over_state", "start_time", "update_time")
	if err != nil {
		logger.Error("Failed to start match", zap.String("match_id", matchID), zap.Error(err))
		c.Data["json"] = utils.ErrorResponse(http.StatusInternalServerError, "Failed to start match")
		c.ServeJSON()
		return
	}

	// TODO: 通知游戏服务器开始比赛
	// err = c.notifyGameServerStartMatch(matchID)
	// if err != nil {
	//     logger.Error("Failed to notify game server", zap.Error(err))
	// }

	logger.Info("Match started successfully", zap.String("match_id", matchID))
	c.Data["json"] = utils.SuccessResponse(nil)
	c.ServeJSON()
}

// StopMatch 停止比赛
// @Title 停止比赛
// @Description 手动停止比赛
// @Param id path string true "比赛ID"
// @Success 200 {object} utils.Response
// @Security ApiKeyAuth
// @router /matches/:id/stop [post]
func (c *GameController) StopMatch() {
	matchID := c.Ctx.Input.Param(":id")
	if matchID == "" {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Match ID is required")
		c.ServeJSON()
		return
	}

	o := database.GetOrm()

	// 查找比赛
	var match models.Match
	err := o.QueryTable("matches").Filter("match_id", matchID).One(&match)
	if err != nil {
		logger.Error("Failed to get match", zap.String("match_id", matchID), zap.Error(err))
		c.Data["json"] = utils.ResourceNotFoundResponse("Match")
		c.ServeJSON()
		return
	}

	// 检查比赛状态
	if match.OverState != 1 {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Match is not running")
		c.ServeJSON()
		return
	}

	// 更新比赛状态
	match.OverState = 2 // 已结束
	match.EndTime = time.Now()

	_, err = o.Update(&match, "over_state", "end_time", "update_time")
	if err != nil {
		logger.Error("Failed to stop match", zap.String("match_id", matchID), zap.Error(err))
		c.Data["json"] = utils.ErrorResponse(http.StatusInternalServerError, "Failed to stop match")
		c.ServeJSON()
		return
	}

	// TODO: 通知游戏服务器停止比赛
	// err = c.notifyGameServerStopMatch(matchID)
	// if err != nil {
	//     logger.Error("Failed to notify game server", zap.Error(err))
	// }

	logger.Info("Match stopped successfully", zap.String("match_id", matchID))
	c.Data["json"] = utils.SuccessResponse(nil)
	c.ServeJSON()
}

// GetGameConfig 获取游戏配置
// @Title 获取游戏配置
// @Description 获取指定游戏类型的配置
// @Param game_type path string true "游戏类型"
// @Success 200 {object} utils.Response{data=models.GameConfig}
// @Security ApiKeyAuth
// @router /config/:game_type [get]
func (c *GameController) GetGameConfig() {
	gameType := c.Ctx.Input.Param(":game_type")
	if gameType == "" {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Game type is required")
		c.ServeJSON()
		return
	}

	o := database.GetOrm()
	config := models.GameConfig{}

	err := o.QueryTable("game_configs").Filter("game_type", gameType).Filter("status", 1).One(&config)
	if err != nil {
		if err == orm.ErrNoRows {
			// 如果没有配置，返回默认配置
			config = models.GameConfig{
				GameType:          gameType,
				RobotEnterTime:    3000,
				DarkPoolThreshold: 0,
				ZuopaiRate:        5000,
				PlayerWinRate:     5000,
				AIWinRate:         5000,
				Status:            1,
			}
		} else {
			logger.Error("Failed to get game config", zap.String("game_type", gameType), zap.Error(err))
			c.Data["json"] = utils.DatabaseErrorResponse(err)
			c.ServeJSON()
			return
		}
	}

	c.Data["json"] = utils.SuccessResponse(config)
	c.ServeJSON()
}

// UpdateGameConfig 更新游戏配置
// @Title 更新游戏配置
// @Description 更新指定游戏类型的配置
// @Param game_type path string true "游戏类型"
// @Param body body models.GameConfigRequest true "游戏配置"
// @Success 200 {object} utils.Response
// @Security ApiKeyAuth
// @router /config/:game_type [post]
func (c *GameController) UpdateGameConfig() {
	gameType := c.Ctx.Input.Param(":game_type")
	if gameType == "" {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Game type is required")
		c.ServeJSON()
		return
	}

	var req models.GameConfigRequest
	if err := c.ParseForm(&req); err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid request format")
		c.ServeJSON()
		return
	}

	o := database.GetOrm()

	// 查找现有配置
	var config models.GameConfig
	err := o.QueryTable("game_configs").Filter("game_type", gameType).One(&config)

	if err == orm.ErrNoRows {
		// 创建新配置
		config = models.GameConfig{
			GameType:                  gameType,
			RobotEnterTime:            req.RobotEnterTime,
			DarkPoolThreshold:         req.DarkPoolThreshold,
			ZuopaiRate:                req.ZuopaiRate,
			MinControlValue:           req.MinControlValue,
			MaxControlValue:           req.MaxControlValue,
			ControlRate:               req.ControlRate,
			PlayerWinRate:             req.PlayerWinRate,
			AIWinRate:                 req.AIWinRate,
			RobotGuestRate:            req.RobotGuestRate,
			PlayerWinnerPreTingRate:   req.PlayerWinnerPreTingRate,
			PlayerWinnerAfterTingRate: req.PlayerWinnerAfterTingRate,
			PlayerLoserPreTingRate:    req.PlayerLoserPreTingRate,
			PlayerLoserAfterTingRate:  req.PlayerLoserAfterTingRate,
			RobotWinnerPreTingRate:    req.RobotWinnerPreTingRate,
			Status:                    1,
		}

		_, err = o.Insert(&config)
		if err != nil {
			logger.Error("Failed to create game config", zap.String("game_type", gameType), zap.Error(err))
			c.Data["json"] = utils.ErrorResponse(http.StatusInternalServerError, "Failed to create config")
			c.ServeJSON()
			return
		}
	} else if err != nil {
		logger.Error("Failed to get game config", zap.String("game_type", gameType), zap.Error(err))
		c.Data["json"] = utils.DatabaseErrorResponse(err)
		c.ServeJSON()
		return
	} else {
		// 更新现有配置
		config.RobotEnterTime = req.RobotEnterTime
		config.DarkPoolThreshold = req.DarkPoolThreshold
		config.ZuopaiRate = req.ZuopaiRate
		config.MinControlValue = req.MinControlValue
		config.MaxControlValue = req.MaxControlValue
		config.ControlRate = req.ControlRate
		config.PlayerWinRate = req.PlayerWinRate
		config.AIWinRate = req.AIWinRate
		config.RobotGuestRate = req.RobotGuestRate
		config.PlayerWinnerPreTingRate = req.PlayerWinnerPreTingRate
		config.PlayerWinnerAfterTingRate = req.PlayerWinnerAfterTingRate
		config.PlayerLoserPreTingRate = req.PlayerLoserPreTingRate
		config.PlayerLoserAfterTingRate = req.PlayerLoserAfterTingRate
		config.RobotWinnerPreTingRate = req.RobotWinnerPreTingRate

		_, err = o.Update(&config)
		if err != nil {
			logger.Error("Failed to update game config", zap.String("game_type", gameType), zap.Error(err))
			c.Data["json"] = utils.ErrorResponse(http.StatusInternalServerError, "Failed to update config")
			c.ServeJSON()
			return
		}
	}

	// TODO: 通知游戏服务器配置更新
	// err = c.notifyGameServerConfigUpdate(gameType, &config)
	// if err != nil {
	//     logger.Error("Failed to notify game server", zap.Error(err))
	// }

	logger.Info("Game config updated successfully", zap.String("game_type", gameType))
	c.Data["json"] = utils.SuccessResponse(nil)
	c.ServeJSON()
}

// GetRobots 获取机器人列表
// @Title 获取机器人列表
// @Description 获取机器人列表
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(20)
// @Param game_type query string false "游戏类型"
// @Success 200 {object} utils.PageResponse{data=[]models.Robot}
// @Security ApiKeyAuth
// @router /robots [get]
func (c *GameController) GetRobots() {
	page, _ := strconv.Atoi(c.GetString("page", "1"))
	size, _ := strconv.Atoi(c.GetString("size", "20"))
	gameType := c.GetString("game_type")

	if page <= 0 {
		page = 1
	}
	if size <= 0 {
		size = 20
	}

	o := database.GetOrm()
	qs := o.QueryTable("robots")

	// 构建查询条件
	if gameType != "" {
		qs = qs.Filter("game_types__icontains", gameType)
	}

	// 获取总数
	total, err := qs.Count()
	if err != nil {
		logger.Error("Failed to count robots", zap.Error(err))
		c.Data["json"] = utils.DatabaseErrorResponse(err)
		c.ServeJSON()
		return
	}

	// 分页查询
	var robots []models.Robot
	offset := (page - 1) * size
	_, err = qs.OrderBy("-create_time").Limit(size, offset).All(&robots)
	if err != nil {
		logger.Error("Failed to get robots", zap.Error(err))
		c.Data["json"] = utils.DatabaseErrorResponse(err)
		c.ServeJSON()
		return
	}

	c.Data["json"] = utils.PageSuccessResponse(robots, total, page, size)
	c.ServeJSON()
}

// UpdateRobots 更新机器人配置
// @Title 更新机器人配置
// @Description 批量更新机器人配置
// @Param body body []models.RobotRequest true "机器人配置列表"
// @Success 200 {object} utils.Response
// @Security ApiKeyAuth
// @router /robots [post]
func (c *GameController) UpdateRobots() {
	var req []models.RobotRequest
	if err := c.ParseForm(&req); err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid request format")
		c.ServeJSON()
		return
	}

	o := database.GetOrm()
	successCount := 0
	failedCount := 0

	for _, robotReq := range req {
		// 查找现有机器人
		var robot models.Robot
		err := o.QueryTable("robots").Filter("user_id", robotReq.UserID).One(&robot)

		if err == orm.ErrNoRows {
			// 创建新机器人
			robot = models.Robot{
				UserID:    robotReq.UserID,
				Nickname:  robotReq.Nickname,
				Avatar:    robotReq.Avatar,
				GameTypes: robotReq.GameTypes,
				WinRate:   robotReq.WinRate,
				Level:     robotReq.Level,
				Status:    1,
			}

			_, err = o.Insert(&robot)
			if err != nil {
				logger.Error("Failed to create robot", zap.Int("user_id", robotReq.UserID), zap.Error(err))
				failedCount++
				continue
			}
		} else if err != nil {
			logger.Error("Failed to get robot", zap.Int("user_id", robotReq.UserID), zap.Error(err))
			failedCount++
			continue
		} else {
			// 更新现有机器人
			robot.Nickname = robotReq.Nickname
			robot.Avatar = robotReq.Avatar
			robot.GameTypes = robotReq.GameTypes
			robot.WinRate = robotReq.WinRate
			robot.Level = robotReq.Level

			_, err = o.Update(&robot)
			if err != nil {
				logger.Error("Failed to update robot", zap.Int("user_id", robotReq.UserID), zap.Error(err))
				failedCount++
				continue
			}
		}

		successCount++
	}

	result := map[string]interface{}{
		"success_count": successCount,
		"failed_count":  failedCount,
		"total_count":   len(req),
	}

	logger.Info("Robots updated",
		zap.Int("success_count", successCount),
		zap.Int("failed_count", failedCount),
	)

	c.Data["json"] = utils.SuccessResponse(result)
	c.ServeJSON()
}

// generateMatchID 生成比赛ID
func (c *GameController) generateMatchID() string {
	return fmt.Sprintf("MATCH_%d", time.Now().UnixNano())
}
