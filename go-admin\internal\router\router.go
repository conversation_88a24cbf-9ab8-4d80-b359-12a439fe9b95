package router

import (
	"kakabs-admin/internal/controllers"
	"kakabs-admin/internal/logger"
	"kakabs-admin/internal/middleware"

	"github.com/beego/beego/v2/server/web"
	"go.uber.org/zap"
)

// Init 初始化路由
func Init() {
	// 设置全局中间件
	web.InsertFilter("*", web.BeforeRouter, middleware.CORSMiddleware)
	web.InsertFilter("/api/*", web.BeforeRouter, middleware.AuthMiddleware)
	web.InsertFilter("*", web.FinishRouter, middleware.LoggingMiddleware)

	// API v1 路由组
	apiV1 := web.NewNamespace("/api/v1",
		// 认证相关路由
		web.NSNamespace("/auth",
			web.NSRouter("/login", &controllers.AuthController{}, "post:Login"),
			web.NSRouter("/logout", &controllers.AuthController{}, "post:Logout"),
			web.NSRouter("/refresh", &controllers.AuthController{}, "post:RefreshToken"),
			web.NSRouter("/profile", &controllers.AuthController{}, "get:Profile"),
			web.NSRouter("/change-password", &controllers.AuthController{}, "post:ChangePassword"),
		),

		// 用户管理路由
		web.NSNamespace("/users",
			web.NSRouter("/", &controllers.UserController{}, "get:List;post:Create"),
			web.NSRouter("/:id", &controllers.UserController{}, "get:Get;put:Update;delete:Delete"),
			web.NSRouter("/:id/ban", &controllers.UserController{}, "post:Ban"),
			web.NSRouter("/:id/unban", &controllers.UserController{}, "post:Unban"),
			web.NSRouter("/:id/gold", &controllers.UserController{}, "post:UpdateGold"),
			web.NSRouter("/:id/diamond", &controllers.UserController{}, "post:UpdateDiamond"),
		),

		// 运营管理路由
		web.NSNamespace("/operation",
			// 道具管理
			web.NSRouter("/items/gift", &controllers.OperationController{}, "post:GiftItems"),
			web.NSRouter("/items/deduct", &controllers.OperationController{}, "post:DeductItems"),
			web.NSRouter("/items/logs", &controllers.OperationController{}, "get:ItemLogs"),
			
			// 公告管理
			web.NSRouter("/announcements", &controllers.OperationController{}, "get:GetAnnouncements;post:CreateAnnouncement"),
			web.NSRouter("/announcements/:id", &controllers.OperationController{}, "put:UpdateAnnouncement;delete:DeleteAnnouncement"),
			
			// 功能开关
			web.NSRouter("/switches", &controllers.OperationController{}, "get:GetSwitches;post:UpdateSwitches"),
			
			// 消息推送
			web.NSRouter("/push", &controllers.OperationController{}, "post:PushMessage"),
			web.NSRouter("/push/logs", &controllers.OperationController{}, "get:PushLogs"),
		),

		// 数据统计路由
		web.NSNamespace("/statistics",
			// 实时数据
			web.NSRouter("/realtime", &controllers.StatisticsController{}, "get:Realtime"),
			web.NSRouter("/online", &controllers.StatisticsController{}, "get:OnlineStats"),
			
			// 用户数据
			web.NSRouter("/users/daily", &controllers.StatisticsController{}, "get:DailyUsers"),
			web.NSRouter("/users/retention", &controllers.StatisticsController{}, "get:UserRetention"),
			
			// 收入数据
			web.NSRouter("/revenue/daily", &controllers.StatisticsController{}, "get:DailyRevenue"),
			web.NSRouter("/revenue/monthly", &controllers.StatisticsController{}, "get:MonthlyRevenue"),
			
			// 游戏数据
			web.NSRouter("/games/summary", &controllers.StatisticsController{}, "get:GameSummary"),
			web.NSRouter("/games/:game_type", &controllers.StatisticsController{}, "get:GameStats"),
		),

		// 支付管理路由
		web.NSNamespace("/payment",
			web.NSRouter("/orders", &controllers.PaymentController{}, "get:GetOrders"),
			web.NSRouter("/orders/:id", &controllers.PaymentController{}, "get:GetOrder"),
			web.NSRouter("/orders/:id/refund", &controllers.PaymentController{}, "post:RefundOrder"),
			web.NSRouter("/statistics", &controllers.PaymentController{}, "get:PaymentStats"),
			web.NSRouter("/config", &controllers.PaymentController{}, "get:GetConfig;post:UpdateConfig"),
		),

		// 游戏管理路由
		web.NSNamespace("/games",
			// 比赛管理
			web.NSRouter("/matches", &controllers.GameController{}, "get:GetMatches;post:CreateMatch"),
			web.NSRouter("/matches/:id", &controllers.GameController{}, "get:GetMatch;put:UpdateMatch;delete:DeleteMatch"),
			web.NSRouter("/matches/:id/start", &controllers.GameController{}, "post:StartMatch"),
			web.NSRouter("/matches/:id/stop", &controllers.GameController{}, "post:StopMatch"),
			
			// 游戏配置
			web.NSRouter("/config/:game_type", &controllers.GameController{}, "get:GetGameConfig;post:UpdateGameConfig"),
			
			// 机器人管理
			web.NSRouter("/robots", &controllers.GameController{}, "get:GetRobots;post:UpdateRobots"),
		),

		// 代理商管理路由
		web.NSNamespace("/agents",
			web.NSRouter("/", &controllers.AgentController{}, "get:List;post:Create"),
			web.NSRouter("/:id", &controllers.AgentController{}, "get:Get;put:Update;delete:Delete"),
			web.NSRouter("/:id/approve", &controllers.AgentController{}, "post:Approve"),
			web.NSRouter("/:id/reject", &controllers.AgentController{}, "post:Reject"),
			web.NSRouter("/applications", &controllers.AgentController{}, "get:GetApplications"),
		),

		// 权限管理路由
		web.NSNamespace("/permissions",
			web.NSRouter("/roles", &controllers.PermissionController{}, "get:GetRoles;post:CreateRole"),
			web.NSRouter("/roles/:id", &controllers.PermissionController{}, "get:GetRole;put:UpdateRole;delete:DeleteRole"),
			web.NSRouter("/rules", &controllers.PermissionController{}, "get:GetRules;post:CreateRule"),
			web.NSRouter("/rules/:id", &controllers.PermissionController{}, "get:GetRule;put:UpdateRule;delete:DeleteRule"),
			web.NSRouter("/admins", &controllers.PermissionController{}, "get:GetAdmins;post:CreateAdmin"),
			web.NSRouter("/admins/:id", &controllers.PermissionController{}, "get:GetAdmin;put:UpdateAdmin;delete:DeleteAdmin"),
		),
	)

	// 注册API路由
	web.AddNamespace(apiV1)

	// 健康检查路由
	web.Router("/health", &controllers.HealthController{}, "get:Check")

	// Swagger文档路由
	if web.BConfig.RunMode == "dev" {
		web.Router("/swagger/*", &controllers.SwaggerController{}, "*:Get")
	}

	logger.Info("Router initialized",
		zap.String("mode", web.BConfig.RunMode),
	)
}

// 定义控制器接口，确保所有控制器都实现基本方法
type BaseController interface {
	Prepare()
	Finish()
}
