# 新支付方式接入工作范本 - 抖音支付

本文档以抖音支付为例，提供完整的新支付方式接入流程，包括开发、配置、测试的全套解决方案。

## 📋 接入流程概览

### 1. 开发阶段
- ✅ 创建支付服务类
- ✅ 扩展支付控制器
- ✅ 添加数据库配置
- ✅ 实现回调处理

### 2. 配置阶段
- ✅ 数据库配置设置
- ✅ 环境变量配置
- ✅ 回调地址配置

### 3. 测试阶段
- ✅ 沙箱环境测试
- ✅ 生产环境小额测试
- ✅ 自动化测试脚本

## 🛠️ 开发实现

### 步骤1: 创建支付服务类

文件: `internal/services/douyin_pay.go`

**核心功能**:
- 订单创建
- 签名生成和验证
- 回调处理
- 订单查询

**关键代码结构**:
```go
type DouyinPayService struct {
    AppID       string
    MerchantID  string
    PrivateKey  string
    PublicKey   string
    NotifyURL   string
    Environment string
}

// 主要方法
func (d *DouyinPayService) CreateOrder(order, product) (*PayResponse, error)
func (d *DouyinPayService) VerifyNotify(notifyData) (*PaymentData, error)
func (d *DouyinPayService) QueryOrder(outOrderNo) (*PaymentData, error)
```

### 步骤2: 扩展支付控制器

文件: `internal/controllers/payment.go`

**新增接口**:
- `POST /api/v1/payment/douyin/create` - 创建抖音支付订单
- `POST /api/v1/payment/douyin/notify` - 抖音支付回调

**接口特点**:
- 统一的请求/响应格式
- 完整的错误处理
- 详细的日志记录
- 事务处理保证数据一致性

### 步骤3: 数据库配置

**支付类型定义**:
- 1: 微信支付
- 2: 支付宝支付
- 3: 苹果内购
- 4: 抖音支付 (新增)

**配置表结构**:
```sql
payment_configs (
    pay_type = 4,           -- 抖音支付
    app_id,                 -- 抖音小程序AppID
    mch_id,                 -- 抖音商户号
    api_key,                -- RSA私钥
    app_secret,             -- 抖音公钥
    notify_url,             -- 回调地址
    environment             -- sandbox/prod
)
```

## ⚙️ 配置设置

### 1. 数据库配置

执行配置脚本:
```bash
mysql -u username -p database_name < scripts/setup_douyin_pay.sql
```

### 2. 环境配置

在 `conf/app.yaml` 中添加:
```yaml
douyin_pay:
  sandbox:
    app_id: "your_sandbox_app_id"
    merchant_id: "your_sandbox_merchant_id"
    private_key: "your_sandbox_private_key"
    public_key: "douyin_sandbox_public_key"
    api_url: "https://open-sandbox.douyin.com"
  
  production:
    app_id: "your_prod_app_id"
    merchant_id: "your_prod_merchant_id"
    private_key: "your_prod_private_key"
    public_key: "douyin_prod_public_key"
    api_url: "https://developer.toutiao.com"
```

### 3. 回调地址配置

**沙箱环境**:
```
https://your-test-domain.com/api/v1/payment/douyin/notify
```

**生产环境**:
```
https://your-prod-domain.com/api/v1/payment/douyin/notify
```

## 🧪 测试方案

### 1. 沙箱环境测试

**自动化测试**:
```bash
# 完整沙箱测试
./scripts/test_payments.sh sandbox-test

# 单独测试抖音支付
./scripts/test_payments.sh sandbox http://localhost:8080 your_token
```

**手动测试**:
```bash
# 1. 测试创建订单
curl -X POST http://localhost:8080/api/v1/payment/douyin/create \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 999999,
    "product_id": 1,
    "pay_method": 1
  }'

# 2. 测试配置获取
curl -X GET http://localhost:8080/api/v1/payment/test/configs \
  -H "Authorization: Bearer your_token"
```

### 2. 生产环境小额测试

**注意事项**:
- ⚠️ 生产环境测试会产生真实费用
- 💰 建议使用0.01元进行测试
- 📝 测试前确保已完成沙箱测试

**测试步骤**:
```bash
# 1. 生产环境小额测试
./scripts/test_payments.sh prod-small

# 2. 手动小额测试
curl -X GET "http://localhost:8080/api/v1/payment/test/small-amount?pay_type=4&amount=0.01" \
  -H "Authorization: Bearer your_token"
```

### 3. 测试验证清单

#### 沙箱环境测试 ✅
- [ ] 订单创建成功
- [ ] 签名生成正确
- [ ] 回调验证通过
- [ ] 订单状态更新
- [ ] 道具发放正常
- [ ] 错误处理正确

#### 生产环境测试 ✅
- [ ] 小额支付成功 (0.01元)
- [ ] 回调接收正常
- [ ] 订单状态同步
- [ ] 用户余额更新
- [ ] 日志记录完整

## 📊 测试报告

### 自动生成测试报告

```bash
# 生成详细测试报告
curl -X GET "http://localhost:8080/api/v1/payment/test/report?environment=sandbox" \
  -H "Authorization: Bearer your_token"
```

**报告内容包括**:
- 测试环境信息
- 各支付方式测试结果
- 成功率统计
- 错误详情分析
- 性能数据 (响应时间)

### 示例测试报告

```markdown
# 支付系统测试报告

测试时间: 2024-12-19 15:30:00
测试环境: sandbox

## 测试结果汇总

- **微信支付** (sandbox): ✅ 成功 - 订单创建成功 (耗时: 245ms)
- **支付宝支付** (sandbox): ✅ 成功 - 订单创建成功 (耗时: 189ms)
- **抖音支付** (sandbox): ✅ 成功 - 订单创建成功 (耗时: 312ms)
- **苹果内购** (sandbox): ✅ 成功 - 收据验证成功 (耗时: 156ms)

**成功率**: 4/4 (100.0%)
```

## 🔧 故障排查

### 常见问题及解决方案

#### 1. 签名验证失败
**问题**: 回调签名验证失败
**解决**: 
- 检查私钥格式是否正确
- 确认签名算法 (RSA2/SHA256)
- 验证参数排序规则

#### 2. 回调地址不可达
**问题**: 支付平台无法访问回调地址
**解决**:
- 确保回调地址可公网访问
- 检查防火墙设置
- 验证SSL证书有效性

#### 3. 订单重复处理
**问题**: 同一订单被多次处理
**解决**:
- 添加订单状态检查
- 使用数据库事务
- 实现幂等性处理

### 调试工具

#### 1. 日志查看
```bash
# 查看支付相关日志
tail -f logs/payment.log | grep "douyin"

# 查看错误日志
tail -f logs/error.log | grep "payment"
```

#### 2. 数据库查询
```sql
-- 查看订单状态
SELECT * FROM payment_orders WHERE pay_type = 4 ORDER BY create_time DESC LIMIT 10;

-- 查看回调记录
SELECT * FROM payment_notify WHERE pay_type = 4 ORDER BY create_time DESC LIMIT 10;

-- 查看配置状态
SELECT * FROM payment_configs WHERE pay_type = 4;
```

## 🚀 上线部署

### 1. 部署前检查

- [ ] 沙箱环境测试通过
- [ ] 生产环境小额测试通过
- [ ] 配置信息已更新为生产环境
- [ ] 回调地址已配置为生产地址
- [ ] 监控和日志已配置

### 2. 部署步骤

```bash
# 1. 更新代码
git pull origin main

# 2. 编译项目
make build

# 3. 更新配置
cp conf/app.prod.yaml conf/app.yaml

# 4. 重启服务
systemctl restart go-admin

# 5. 验证服务
curl -X GET http://localhost:8080/health
```

### 3. 上线后验证

```bash
# 运行生产环境测试
./scripts/test_payments.sh prod https://api.yourdomain.com your_prod_token

# 检查服务状态
systemctl status go-admin

# 查看实时日志
journalctl -u go-admin -f
```

## 📈 监控和维护

### 1. 关键指标监控

- 支付成功率
- 回调处理成功率
- 平均响应时间
- 错误率统计

### 2. 告警设置

- 支付成功率低于95%
- 回调处理失败率超过1%
- 响应时间超过5秒
- 连续错误超过10次

### 3. 定期维护

- 每日检查支付数据
- 每周生成测试报告
- 每月更新证书和密钥
- 季度性能优化

---

## 📝 总结

通过本工作范本，您可以：

1. **快速接入新支付方式** - 完整的代码模板和配置示例
2. **确保接入质量** - 全面的测试方案和验证清单
3. **降低接入风险** - 详细的故障排查和监控方案
4. **提高开发效率** - 自动化测试脚本和部署流程

这套方案已经在抖音支付接入中得到验证，可以作为接入其他支付方式的标准模板。
