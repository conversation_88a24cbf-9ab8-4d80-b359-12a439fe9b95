-- 抖音支付配置设置脚本
-- 使用方法: mysql -u username -p database_name < setup_douyin_pay.sql

-- 1. 添加抖音支付配置
INSERT INTO payment_configs (
    pay_type,
    pay_type_name,
    app_id,
    mch_id,
    api_key,
    app_secret,
    notify_url,
    return_url,
    environment,
    status,
    create_time,
    update_time
) VALUES (
    4,
    '抖音支付',
    'your_douyin_app_id',                    -- 替换为实际的抖音小程序AppID
    'your_douyin_merchant_id',               -- 替换为实际的抖音商户号
    'your_douyin_private_key',               -- 替换为实际的RSA私钥
    'your_douyin_public_key',                -- 替换为实际的抖音公钥
    'https://your-domain.com/api/v1/payment/douyin/notify',  -- 替换为实际的回调地址
    'https://your-domain.com/payment/result',                -- 替换为实际的返回地址
    'sandbox',                               -- 环境: sandbox/prod
    1,                                       -- 状态: 1启用 0禁用
    NOW(),
    NOW()
) ON DUPLICATE KEY UPDATE
    app_id = VALUES(app_id),
    mch_id = VALUES(mch_id),
    api_key = VALUES(api_key),
    app_secret = VALUES(app_secret),
    notify_url = VALUES(notify_url),
    return_url = VALUES(return_url),
    environment = VALUES(environment),
    status = VALUES(status),
    update_time = NOW();

-- 2. 添加测试商品
INSERT INTO payment_products (
    name,
    description,
    price,
    currency,
    item_type,
    item_count,
    bonus_count,
    status,
    create_time,
    update_time
) VALUES 
(
    '测试商品-1分钱',
    '抖音支付测试商品',
    0.01,
    'CNY',
    'coin',
    100,
    0,
    1,
    NOW(),
    NOW()
),
(
    '测试商品-1元',
    '抖音支付测试商品',
    1.00,
    'CNY',
    'coin',
    10000,
    1000,
    1,
    NOW(),
    NOW()
) ON DUPLICATE KEY UPDATE
    price = VALUES(price),
    description = VALUES(description),
    update_time = NOW();

-- 3. 创建测试用户（如果不存在）
INSERT IGNORE INTO users (
    userid,
    nickname,
    avatar,
    coin,
    diamond,
    status,
    reg_time,
    last_login_time
) VALUES (
    999999,
    '支付测试用户',
    'https://example.com/test-avatar.jpg',
    0,
    0,
    1,
    UNIX_TIMESTAMP(),
    UNIX_TIMESTAMP()
);

-- 4. 查看配置结果
SELECT 
    id,
    pay_type,
    pay_type_name,
    app_id,
    mch_id,
    LEFT(api_key, 10) as api_key_preview,
    LEFT(app_secret, 10) as app_secret_preview,
    notify_url,
    environment,
    status,
    create_time
FROM payment_configs 
WHERE pay_type = 4;

-- 5. 查看测试商品
SELECT 
    id,
    name,
    description,
    price,
    currency,
    item_type,
    item_count,
    bonus_count,
    status
FROM payment_products 
WHERE name LIKE '%测试商品%';

-- 6. 显示所有支付方式配置状态
SELECT 
    pay_type,
    pay_type_name,
    environment,
    status,
    CASE 
        WHEN status = 1 THEN '✅ 启用'
        ELSE '❌ 禁用'
    END as status_text
FROM payment_configs 
ORDER BY pay_type;
