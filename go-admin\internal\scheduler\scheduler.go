package scheduler

import (
	"kakabs-admin/internal/config"
	"kakabs-admin/internal/logger"

	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
)

var cronScheduler *cron.Cron

// Init 初始化定时任务调度器
func Init() {
	if !config.GetBool("scheduler.enabled") {
		logger.Info("Scheduler is disabled")
		return
	}

	// 创建定时任务调度器
	cronScheduler = cron.New(cron.WithSeconds())

	// 添加定时任务
	addJobs()

	// 启动调度器
	cronScheduler.Start()

	logger.Info("Scheduler initialized and started")
}

// addJobs 添加定时任务
func addJobs() {
	// 每分钟执行一次在线用户统计
	cronScheduler.AddFunc("0 * * * * *", onlineStatsJob)

	// 每小时执行一次数据汇总
	cronScheduler.AddFunc("0 0 * * * *", hourlyDataJob)

	// 每天凌晨执行日报统计
	cronScheduler.AddFunc("0 0 0 * * *", dailyReportJob)

	// 每周一凌晨执行周报统计
	cronScheduler.AddFunc("0 0 0 * * 1", weeklyReportJob)

	logger.Info("Scheduled jobs added")
}

// onlineStatsJob 在线用户统计任务
func onlineStatsJob() {
	logger.Debug("Running online stats job")
	// TODO: 实现在线用户统计逻辑
	// 1. 统计各游戏模式在线人数
	// 2. 记录到数据库
	// 3. 更新缓存
}

// hourlyDataJob 每小时数据汇总任务
func hourlyDataJob() {
	logger.Debug("Running hourly data job")
	// TODO: 实现每小时数据汇总逻辑
	// 1. 汇总游戏数据
	// 2. 汇总收入数据
	// 3. 汇总用户活跃数据
}

// dailyReportJob 日报统计任务
func dailyReportJob() {
	logger.Debug("Running daily report job")
	// TODO: 实现日报统计逻辑
	// 1. 生成日活跃用户报告
	// 2. 生成日收入报告
	// 3. 生成游戏数据报告
	// 4. 发送报告邮件/通知
}

// weeklyReportJob 周报统计任务
func weeklyReportJob() {
	logger.Debug("Running weekly report job")
	// TODO: 实现周报统计逻辑
	// 1. 生成周活跃用户报告
	// 2. 生成周收入报告
	// 3. 生成用户留存报告
	// 4. 发送报告邮件/通知
}

// Stop 停止调度器
func Stop() {
	if cronScheduler != nil {
		cronScheduler.Stop()
		logger.Info("Scheduler stopped")
	}
}

// AddJob 动态添加定时任务
func AddJob(spec string, cmd func()) (cron.EntryID, error) {
	if cronScheduler == nil {
		return 0, nil
	}
	return cronScheduler.AddFunc(spec, cmd)
}

// RemoveJob 移除定时任务
func RemoveJob(id cron.EntryID) {
	if cronScheduler != nil {
		cronScheduler.Remove(id)
	}
}
