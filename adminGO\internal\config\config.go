package config

import (
	"os"
	"strconv"
)

// Config 应用配置
type Config struct {
	// 数据库配置
	DBUser     string
	DBPassword string
	DBHost     string
	DBPort     int
	DBName     string

	// 服务器配置
	ServerPort int
}

// LoadConfig 从环境变量加载配置
func LoadConfig() *Config {
	config := &Config{}

	// 加载数据库配置
	config.DBUser = getEnv("DB_USER", "xy_game001")
	config.DBPassword = getEnv("DB_PASSWORD", "password")
	config.DBHost = getEnv("DB_HOST", "rm-uf669j24x47w63g28.mysql.rds.aliyuncs.com")
	config.DBPort = getEnvAsInt("DB_PORT", 3306)
	config.DBName = getEnv("DB_NAME", "match_center")

	// 加载服务器配置
	config.ServerPort = getEnvAsInt("SERVER_PORT", 8080)

	return config
}

// getEnv 从环境变量获取字符串值，如果不存在则使用默认值
func getEnv(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}

// getEnvAsInt 从环境变量获取整数值，如果不存在或解析失败则使用默认值
func getEnvAsInt(key string, defaultValue int) int {
	valueStr := os.Getenv(key)
	if valueStr == "" {
		return defaultValue
	}

	value, err := strconv.Atoi(valueStr)
	if err != nil {
		return defaultValue
	}
	return value
}