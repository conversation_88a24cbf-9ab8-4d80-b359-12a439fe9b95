package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"kakabs-admin/internal/logger"
	"kakabs-admin/internal/models"
	"net/http"
	"time"

	"go.uber.org/zap"
)

// ApplePayService 苹果内购验证服务
type ApplePayService struct {
	Environment string // sandbox/production
}

// NewApplePayService 创建苹果内购服务实例
func NewApplePayService(environment string) *ApplePayService {
	return &ApplePayService{
		Environment: environment,
	}
}

// ReceiptVerifyRequest 收据验证请求
type ReceiptVerifyRequest struct {
	ReceiptData string `json:"receipt-data"`
	Password    string `json:"password,omitempty"` // 共享密钥，用于自动续订订阅
}

// ReceiptVerifyResponse 收据验证响应
type ReceiptVerifyResponse struct {
	Status             int                `json:"status"`
	Environment        string             `json:"environment"`
	Receipt            Receipt            `json:"receipt"`
	LatestReceiptInfo  []InAppPurchase    `json:"latest_receipt_info"`
	PendingRenewalInfo []RenewalInfo      `json:"pending_renewal_info"`
	IsRetryable        bool               `json:"is-retryable"`
}

// Receipt 收据信息
type Receipt struct {
	ReceiptType                string          `json:"receipt_type"`
	AdamID                     int64           `json:"adam_id"`
	AppItemID                  int64           `json:"app_item_id"`
	BundleID                   string          `json:"bundle_id"`
	ApplicationVersion         string          `json:"application_version"`
	DownloadID                 int64           `json:"download_id"`
	VersionExternalIdentifier  int64           `json:"version_external_identifier"`
	ReceiptCreationDate        string          `json:"receipt_creation_date"`
	ReceiptCreationDateMS      string          `json:"receipt_creation_date_ms"`
	ReceiptCreationDatePST     string          `json:"receipt_creation_date_pst"`
	RequestDate                string          `json:"request_date"`
	RequestDateMS              string          `json:"request_date_ms"`
	RequestDatePST             string          `json:"request_date_pst"`
	OriginalPurchaseDate       string          `json:"original_purchase_date"`
	OriginalPurchaseDateMS     string          `json:"original_purchase_date_ms"`
	OriginalPurchaseDatePST    string          `json:"original_purchase_date_pst"`
	OriginalApplicationVersion string          `json:"original_application_version"`
	InApp                      []InAppPurchase `json:"in_app"`
}

// InAppPurchase 内购信息
type InAppPurchase struct {
	Quantity                string `json:"quantity"`
	ProductID               string `json:"product_id"`
	TransactionID           string `json:"transaction_id"`
	OriginalTransactionID   string `json:"original_transaction_id"`
	PurchaseDate            string `json:"purchase_date"`
	PurchaseDateMS          string `json:"purchase_date_ms"`
	PurchaseDatePST         string `json:"purchase_date_pst"`
	OriginalPurchaseDate    string `json:"original_purchase_date"`
	OriginalPurchaseDateMS  string `json:"original_purchase_date_ms"`
	OriginalPurchaseDatePST string `json:"original_purchase_date_pst"`
	ExpiresDate             string `json:"expires_date"`
	ExpiresDateMS           string `json:"expires_date_ms"`
	ExpiresDatePST          string `json:"expires_date_pst"`
	WebOrderLineItemID      string `json:"web_order_line_item_id"`
	IsTrialPeriod           string `json:"is_trial_period"`
	IsInIntroOfferPeriod    string `json:"is_in_intro_offer_period"`
}

// RenewalInfo 续订信息
type RenewalInfo struct {
	ExpirationIntent   string `json:"expiration_intent"`
	AutoRenewProductID string `json:"auto_renew_product_id"`
	RetryFlag          string `json:"retry_flag"`
	AutoRenewStatus    string `json:"auto_renew_status"`
	PriceConsentStatus string `json:"price_consent_status"`
	ProductID          string `json:"product_id"`
}

// VerifyReceipt 验证苹果内购收据
func (a *ApplePayService) VerifyReceipt(receiptData string, sharedSecret string) (*ReceiptVerifyResponse, error) {
	// 构建验证请求
	request := ReceiptVerifyRequest{
		ReceiptData: receiptData,
		Password:    sharedSecret,
	}

	// 首先尝试生产环境
	response, err := a.sendVerifyRequest(request, false)
	if err != nil {
		return nil, fmt.Errorf("verify receipt failed: %w", err)
	}

	// 如果生产环境返回21007错误码，说明是沙盒收据，需要到沙盒环境验证
	if response.Status == 21007 {
		logger.Info("Receipt is from sandbox, retrying with sandbox environment")
		response, err = a.sendVerifyRequest(request, true)
		if err != nil {
			return nil, fmt.Errorf("verify receipt in sandbox failed: %w", err)
		}
	}

	return response, nil
}

// sendVerifyRequest 发送验证请求
func (a *ApplePayService) sendVerifyRequest(request ReceiptVerifyRequest, sandbox bool) (*ReceiptVerifyResponse, error) {
	// 选择验证URL
	verifyURL := "https://buy.itunes.apple.com/verifyReceipt"
	if sandbox {
		verifyURL = "https://sandbox.itunes.apple.com/verifyReceipt"
	}

	// 序列化请求数据
	requestData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("marshal request failed: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", verifyURL, bytes.NewBuffer(requestData))
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("send request failed: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response failed: %w", err)
	}

	// 解析响应
	var verifyResponse ReceiptVerifyResponse
	err = json.Unmarshal(responseData, &verifyResponse)
	if err != nil {
		return nil, fmt.Errorf("unmarshal response failed: %w", err)
	}

	logger.Debug("Apple receipt verify response",
		zap.Int("status", verifyResponse.Status),
		zap.String("environment", verifyResponse.Environment),
		zap.Bool("sandbox", sandbox),
	)

	return &verifyResponse, nil
}

// ProcessPurchase 处理内购
func (a *ApplePayService) ProcessPurchase(order *models.PaymentOrder, receiptData string) error {
	// 验证收据
	response, err := a.VerifyReceipt(receiptData, "")
	if err != nil {
		return fmt.Errorf("verify receipt failed: %w", err)
	}

	// 检查验证状态
	if response.Status != 0 {
		return fmt.Errorf("receipt verification failed with status: %d", response.Status)
	}

	// 查找对应的内购项目
	var targetPurchase *InAppPurchase
	for _, purchase := range response.Receipt.InApp {
		if purchase.ProductID == order.ProductName { // 这里假设ProductName存储的是苹果产品ID
			targetPurchase = &purchase
			break
		}
	}

	if targetPurchase == nil {
		return fmt.Errorf("purchase not found in receipt")
	}

	// 更新订单信息
	order.TransactionID = targetPurchase.TransactionID
	order.Status = 1 // 已支付
	order.PayStatus = 1 // 支付成功
	order.PayTime = time.Now()

	logger.Info("Apple purchase processed successfully",
		zap.String("transaction_id", targetPurchase.TransactionID),
		zap.String("product_id", targetPurchase.ProductID),
		zap.String("order_id", order.OrderID),
	)

	return nil
}

// GetStatusMessage 获取状态码对应的消息
func (a *ApplePayService) GetStatusMessage(status int) string {
	statusMessages := map[int]string{
		0:     "验证成功",
		21000: "App Store无法读取你提供的JSON数据",
		21002: "收据数据不符合格式",
		21003: "收据无法被验证",
		21004: "你提供的共享密钥和账户的共享密钥不一致",
		21005: "收据服务器当前不可用",
		21006: "收据是有效的，但订阅服务已经过期",
		21007: "收据信息是测试用（sandbox），但却被发送到产品环境中验证",
		21008: "收据信息是产品环境中的，但却被发送到测试环境中验证",
		21009: "内部数据访问错误",
		21010: "用户账户无法找到或已被删除",
	}

	if message, exists := statusMessages[status]; exists {
		return message
	}
	return fmt.Sprintf("未知状态码: %d", status)
}
