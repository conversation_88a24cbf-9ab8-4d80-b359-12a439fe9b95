package models

import (
	"time"

	"github.com/beego/beego/v2/client/orm"
)

// Admin 管理员模型
type Admin struct {
	ID            int       `orm:"auto;pk;column(id)" json:"id"`
	AdminName     string    `orm:"size(255);column(admin_name)" json:"admin_name"`
	AdminPassword string    `orm:"size(255);column(admin_password)" json:"-"`
	Time          time.Time `orm:"auto_now_add;type(datetime);column(time)" json:"time"`
	AdminType     int       `orm:"column(admin_type)" json:"admin_type"` // 1=总后台 2=代理后台
	ChannelID     int       `orm:"column(channel_id)" json:"channel_id"` // 0=总渠道 1=杨总代
}

// TableName 设置表名
func (a *Admin) TableName() string {
	return "admin"
}

// AdminNav 管理员导航菜单模型
type AdminNav struct {
	ID          int    `orm:"pk;column(id)" json:"id"`
	PID         int    `orm:"column(pid)" json:"pid"`
	Name        string `orm:"size(15);column(name)" json:"name"`
	MCA         string `orm:"size(500);column(mca)" json:"mca"`
	ICO         string `orm:"size(20);column(ico)" json:"ico"`
	OrderNumber int    `orm:"column(order_number)" json:"order_number"`
}

// TableName 设置表名
func (a *AdminNav) TableName() string {
	return "admin_nav"
}

// AuthGroup 权限组模型
type AuthGroup struct {
	ID     int    `orm:"pk;column(id)" json:"id"`
	Title  string `orm:"size(100);column(title)" json:"title"`
	Status int    `orm:"column(status);default(1)" json:"status"`
	Rules  string `orm:"type(text);column(rules)" json:"rules"`
}

// TableName 设置表名
func (a *AuthGroup) TableName() string {
	return "auth_group"
}

// AuthGroupAccess 权限组用户关联模型
type AuthGroupAccess struct {
	UID     int `orm:"column(uid)" json:"uid"`
	GroupID int `orm:"column(group_id)" json:"group_id"`
}

// TableName 设置表名
func (a *AuthGroupAccess) TableName() string {
	return "auth_group_access"
}

// AuthRule 权限规则模型
type AuthRule struct {
	ID        int    `orm:"pk;column(id)" json:"id"`
	PID       int    `orm:"column(pid)" json:"pid"`
	Name      string `orm:"type(text);column(name)" json:"name"`
	Title     string `orm:"size(20);column(title)" json:"title"`
	Status    int    `orm:"column(status);default(1)" json:"status"`
	Type      int    `orm:"column(type)" json:"type"`
	Condition string `orm:"size(100);column(condition)" json:"condition"`
}

// TableName 设置表名
func (a *AuthRule) TableName() string {
	return "auth_rule"
}

// AuroraePush 极光推送模型
type AuroraPush struct {
	ID        int       `orm:"auto;pk;column(id)" json:"id"`
	Type      int       `orm:"column(type);default(0)" json:"type"` // 0文字发送 1图文发送
	AdminName string    `orm:"size(20);column(admin_name)" json:"admin_name"`
	Desc      string    `orm:"size(120);column(desc)" json:"desc"`
	Text      string    `orm:"type(text);column(text)" json:"text"`
	CreatedAt time.Time `orm:"auto_now_add;type(timestamp);column(created_at)" json:"created_at"`
}

// TableName 设置表名
func (a *AuroraPush) TableName() string {
	return "aurora_push"
}

// AutoReloadLog 自动重载日志模型
type AutoReloadLog struct {
	ID          int64     `orm:"auto;pk;column(id)" json:"id"`
	Table       string    `orm:"size(255);column(table_name)" json:"table_name"`
	ServerGroup int       `orm:"column(server_group);default(0)" json:"server_group"`
	ServerLine  int       `orm:"column(server_line);default(0)" json:"server_line"`
	UTCTime     int       `orm:"column(utc_time)" json:"utc_time"`
	Lua         int       `orm:"column(lua);default(0)" json:"lua"`
	Type        int       `orm:"column(type);default(0)" json:"type"` // 0:未更新过数据 1:更新过数据
	ViewTime    time.Time `orm:"auto_now_add;type(datetime);column(view_time)" json:"view_time"`
}

// TableName 设置表名
func (a *AutoReloadLog) TableName() string {
	return "auto_reload_log"
}

// AlsoOnlineLog 同时在线日志模型
type AlsoOnlineLog struct {
	ID                int64     `orm:"auto;pk;column(id)" json:"id"`
	AllPeopleNum      int       `orm:"column(all_people_num);default(0)" json:"all_people_num"`
	XueliuPeopleNumD  int       `orm:"column(xueliu_people_num_d);default(0)" json:"xueliu_people_num_d"`
	XueliuPeopleNumC  int       `orm:"column(xueliu_people_num_c);default(0)" json:"xueliu_people_num_c"`
	XueliuPeopleNumZ  int       `orm:"column(xueliu_people_num_z);default(0)" json:"xueliu_people_num_z"`
	XueliuPeopleNumG  int       `orm:"column(xueliu_people_num_g);default(0)" json:"xueliu_people_num_g"`
	XuezhanPeopleNumD int       `orm:"column(xuezhan_people_num_d);default(0)" json:"xuezhan_people_num_d"`
	XuezhanPeopleNumC int       `orm:"column(xuezhan_people_num_c);default(0)" json:"xuezhan_people_num_c"`
	XuezhanPeopleNumZ int       `orm:"column(xuezhan_people_num_z);default(0)" json:"xuezhan_people_num_z"`
	XuezhanPeopleNumG int       `orm:"column(xuezhan_people_num_g);default(0)" json:"xuezhan_people_num_g"`
	TuidaohPeopleNumD int       `orm:"column(tuidaoh_people_num_d);default(0)" json:"tuidaoh_people_num_d"`
	TuidaohPeopleNumC int       `orm:"column(tuidaoh_people_num_c);default(0)" json:"tuidaoh_people_num_c"`
	TuidaohPeopleNumZ int       `orm:"column(tuidaoh_people_num_z);default(0)" json:"tuidaoh_people_num_z"`
	TuidaohPeopleNumG int       `orm:"column(tuidaoh_people_num_g);default(0)" json:"tuidaoh_people_num_g"`
	Time              int       `orm:"column(time);default(0)" json:"time"`
	ViewTime          time.Time `orm:"auto_now_add;type(datetime);column(view_time)" json:"view_time"`
}

// TableName 设置表名
func (a *AlsoOnlineLog) TableName() string {
	return "also_online_log"
}

func init() {
	// 注册模型到admin数据库
	orm.RegisterModelWithPrefix("", new(Admin))
	orm.RegisterModelWithPrefix("", new(AdminNav))
	orm.RegisterModelWithPrefix("", new(AuthGroup))
	orm.RegisterModelWithPrefix("", new(AuthGroupAccess))
	orm.RegisterModelWithPrefix("", new(AuthRule))
	orm.RegisterModelWithPrefix("", new(AuroraPush))
	orm.RegisterModelWithPrefix("", new(AutoReloadLog))
	orm.RegisterModelWithPrefix("", new(AlsoOnlineLog))
}
