package models

import (
	"time"

	"github.com/beego/beego/v2/client/orm"
)

// DayActive 日活跃统计模型
type DayActive struct {
	ID            int       `orm:"auto;pk;column(id)" json:"id"`
	Time          string    `orm:"size(20);column(time)" json:"time"`                     // 日期 YYYY-MM-DD
	NewAddNum     int       `orm:"column(new_add_num);default(0)" json:"new_add_num"`     // 新增用户数
	UserActiveNum int       `orm:"column(user_active_num);default(0)" json:"user_active_num"` // 活跃用户数
	ChannelID     int       `orm:"column(channel_id);default(0)" json:"channel_id"`       // 渠道ID
	CreateTime    time.Time `orm:"auto_now_add;type(datetime);column(create_time)" json:"create_time"`
}

// TableName 设置表名
func (d *DayActive) TableName() string {
	return "day_active"
}

// IncomeDay 日收入统计模型
type IncomeDay struct {
	ID           int       `orm:"auto;pk;column(id)" json:"id"`
	IncomeDate   int       `orm:"column(income_date)" json:"income_date"`               // 收入日期时间戳
	PayMoney     float64   `orm:"column(pay_money);default(0)" json:"pay_money"`       // 充值金额
	PayVas       int64     `orm:"column(pay_vas);default(0)" json:"pay_vas"`           // 充值钻石
	FreeVas      int64     `orm:"column(free_vas);default(0)" json:"free_vas"`         // 赠送钻石
	SignAmount   float64   `orm:"column(sign_amount);default(0)" json:"sign_amount"`   // 报名回收
	RebornAmount float64   `orm:"column(reborn_amount);default(0)" json:"reborn_amount"` // 买活回收
	RewardAmount float64   `orm:"column(reward_amount);default(0)" json:"reward_amount"` // 派奖支出
	FinalIncome  float64   `orm:"column(final_income);default(0)" json:"final_income"` // 最终收益
	ChannelID    int       `orm:"column(channel_id);default(0)" json:"channel_id"`     // 渠道ID
	CreateTime   time.Time `orm:"auto_now_add;type(datetime);column(create_time)" json:"create_time"`
}

// TableName 设置表名
func (i *IncomeDay) TableName() string {
	return "income_day"
}

// PayLog 支付日志模型
type PayLog struct {
	ID            int       `orm:"auto;pk;column(id)" json:"id"`
	UserID        int       `orm:"column(user_id)" json:"user_id"`
	PayType       int       `orm:"column(pay_type)" json:"pay_type"`                   // 支付类型 1金豆 2钻石 3体力
	PayMoney      float64   `orm:"column(pay_money);default(0)" json:"pay_money"`      // 支付金额
	PayVas        int64     `orm:"column(pay_vas);default(0)" json:"pay_vas"`          // 支付钻石数
	FreeVas       int64     `orm:"column(free_vas);default(0)" json:"free_vas"`        // 赠送钻石数
	CheckPayState int       `orm:"column(check_pay_state);default(0)" json:"check_pay_state"` // 支付状态
	Used          int       `orm:"column(used);default(0)" json:"used"`                // 是否使用
	LogDate       time.Time `orm:"type(datetime);column(log_date)" json:"log_date"`    // 日志时间
	ChannelID     int       `orm:"column(channel_id);default(0)" json:"channel_id"`    // 渠道ID
	CreateTime    time.Time `orm:"auto_now_add;type(datetime);column(create_time)" json:"create_time"`
}

// TableName 设置表名
func (p *PayLog) TableName() string {
	return "pay_log"
}

// AlsoOnlineLog 同时在线日志模型 (已在admin.go中定义，这里重新定义用于统计)
type OnlineStats struct {
	ID                   int64     `orm:"auto;pk;column(id)" json:"id"`
	AllPeopleNum         int       `orm:"column(all_people_num);default(0)" json:"all_people_num"`
	XueliuPeopleNumD     int       `orm:"column(xueliu_people_num_d);default(0)" json:"xueliu_people_num_d"`
	XueliuPeopleNumC     int       `orm:"column(xueliu_people_num_c);default(0)" json:"xueliu_people_num_c"`
	XueliuPeopleNumZ     int       `orm:"column(xueliu_people_num_z);default(0)" json:"xueliu_people_num_z"`
	XueliuPeopleNumG     int       `orm:"column(xueliu_people_num_g);default(0)" json:"xueliu_people_num_g"`
	XuezhanPeopleNumD    int       `orm:"column(xuezhan_people_num_d);default(0)" json:"xuezhan_people_num_d"`
	XuezhanPeopleNumC    int       `orm:"column(xuezhan_people_num_c);default(0)" json:"xuezhan_people_num_c"`
	XuezhanPeopleNumZ    int       `orm:"column(xuezhan_people_num_z);default(0)" json:"xuezhan_people_num_z"`
	XuezhanPeopleNumG    int       `orm:"column(xuezhan_people_num_g);default(0)" json:"xuezhan_people_num_g"`
	TuidaohPeopleNumD    int       `orm:"column(tuidaoh_people_num_d);default(0)" json:"tuidaoh_people_num_d"`
	TuidaohPeopleNumC    int       `orm:"column(tuidaoh_people_num_c);default(0)" json:"tuidaoh_people_num_c"`
	TuidaohPeopleNumZ    int       `orm:"column(tuidaoh_people_num_z);default(0)" json:"tuidaoh_people_num_z"`
	TuidaohPeopleNumG    int       `orm:"column(tuidaoh_people_num_g);default(0)" json:"tuidaoh_people_num_g"`
	Time                 int       `orm:"column(time);default(0)" json:"time"`
	ViewTime             time.Time `orm:"auto_now_add;type(datetime);column(view_time)" json:"view_time"`
}

// TableName 设置表名
func (o *OnlineStats) TableName() string {
	return "also_online_log"
}

// UserRetention 用户留存统计模型
type UserRetention struct {
	ID              int       `orm:"auto;pk;column(id)" json:"id"`
	StatisticDate   string    `orm:"size(20);column(statistic_date)" json:"statistic_date"` // 统计日期
	NewlyMembers    int       `orm:"column(newly_members);default(0)" json:"newly_members"` // 新增用户数
	ActivityMembers int       `orm:"column(activity_members);default(0)" json:"activity_members"` // 活跃用户数
	PartinMembers   int       `orm:"column(partin_members);default(0)" json:"partin_members"`     // 参与用户数
	PartinPercent   float64   `orm:"column(partin_percent);default(0)" json:"partin_percent"`     // 参与率
	NextdayRemain   float64   `orm:"column(nextday_remain);default(0)" json:"nextday_remain"`     // 次日留存
	ThreedayRemain  float64   `orm:"column(threeday_remain);default(0)" json:"threeday_remain"`   // 三日留存
	SevendayRemain  float64   `orm:"column(sevenday_remain);default(0)" json:"sevenday_remain"`   // 七日留存
	ChannelID       int       `orm:"column(channel_id);default(0)" json:"channel_id"`             // 渠道ID
	CreateTime      time.Time `orm:"auto_now_add;type(datetime);column(create_time)" json:"create_time"`
}

// TableName 设置表名
func (u *UserRetention) TableName() string {
	return "member_activity_log"
}

// GameStats 游戏统计模型
type GameStats struct {
	ID         int       `orm:"auto;pk;column(id)" json:"id"`
	GameType   string    `orm:"size(50);column(game_type)" json:"game_type"`         // 游戏类型
	Date       string    `orm:"size(20);column(date)" json:"date"`                   // 统计日期
	DeskCount  int       `orm:"column(desk_count);default(0)" json:"desk_count"`     // 开局数
	PlayerCount int      `orm:"column(player_count);default(0)" json:"player_count"` // 参与人数
	FkConsume  int64     `orm:"column(fk_consume);default(0)" json:"fk_consume"`     // 体力消耗
	GoldFlow   int64     `orm:"column(gold_flow);default(0)" json:"gold_flow"`       // 金豆流水
	ChannelID  int       `orm:"column(channel_id);default(0)" json:"channel_id"`     // 渠道ID
	CreateTime time.Time `orm:"auto_now_add;type(datetime);column(create_time)" json:"create_time"`
}

// TableName 设置表名
func (g *GameStats) TableName() string {
	return "game_stats"
}

// RealtimeStats 实时统计数据结构
type RealtimeStats struct {
	OnlineUsers    int     `json:"online_users"`    // 当前在线用户数
	TodayNewUsers  int     `json:"today_new_users"` // 今日新增用户
	TodayActive    int     `json:"today_active"`    // 今日活跃用户
	TodayRevenue   float64 `json:"today_revenue"`   // 今日收入
	TodayMaxOnline int     `json:"today_max_online"` // 今日最高在线
	GameCenterOnline int   `json:"game_center_online"` // 游戏中心在线
	GameHouseOnline  int   `json:"game_house_online"`  // 茶馆在线
}

// DashboardStats 仪表盘统计数据
type DashboardStats struct {
	TotalUsers     int     `json:"total_users"`     // 总用户数
	TodayNewUsers  int     `json:"today_new_users"` // 今日新增
	TodayActive    int     `json:"today_active"`    // 今日活跃
	YesterdayNew   int     `json:"yesterday_new"`   // 昨日新增
	YesterdayActive int    `json:"yesterday_active"` // 昨日活跃
	NewUserTrend   int     `json:"new_user_trend"`  // 新增趋势(相比昨日)
	ActiveTrend    int     `json:"active_trend"`    // 活跃趋势(相比昨日)
	TodayRevenue   float64 `json:"today_revenue"`   // 今日收入
	MonthRevenue   float64 `json:"month_revenue"`   // 本月收入
	OnlineUsers    int     `json:"online_users"`    // 当前在线
	MaxOnlineToday int     `json:"max_online_today"` // 今日最高在线
}

// ChartData 图表数据结构
type ChartData struct {
	Labels []string    `json:"labels"` // X轴标签
	Data   [][]float64 `json:"data"`   // 数据系列
	Series []string    `json:"series"` // 数据系列名称
}

// UserActivityChart 用户活跃图表数据
type UserActivityChart struct {
	Dates      []string `json:"dates"`       // 日期
	NewUsers   []int    `json:"new_users"`   // 新增用户
	ActiveUsers []int   `json:"active_users"` // 活跃用户
}

// RevenueChart 收入图表数据
type RevenueChart struct {
	Dates    []string  `json:"dates"`     // 日期
	Revenue  []float64 `json:"revenue"`   // 收入
	PayCount []int     `json:"pay_count"` // 付费人数
}

// OnlineChart 在线用户图表数据
type OnlineChart struct {
	Times       []string `json:"times"`        // 时间点
	OnlineUsers []int    `json:"online_users"` // 在线用户数
}

func init() {
	// 注册统计相关模型
	orm.RegisterModelWithPrefix("", new(DayActive))
	orm.RegisterModelWithPrefix("", new(IncomeDay))
	orm.RegisterModelWithPrefix("", new(PayLog))
	orm.RegisterModelWithPrefix("", new(OnlineStats))
	orm.RegisterModelWithPrefix("", new(UserRetention))
	orm.RegisterModelWithPrefix("", new(GameStats))
}
