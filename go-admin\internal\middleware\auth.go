package middleware

import (
	"kakabs-admin/internal/config"
	"kakabs-admin/internal/logger"
	"kakabs-admin/internal/utils"
	"net/http"
	"strings"

	"github.com/beego/beego/v2/server/web/context"
	"go.uber.org/zap"
)

// AuthMiddleware JWT认证中间件
func AuthMiddleware(ctx *context.Context) {
	// 跳过认证的路径
	skipPaths := []string{
		"/api/v1/auth/login",
		"/api/v1/auth/logout",
		"/swagger",
		"/static",
		"/health",
	}

	path := ctx.Request.URL.Path
	for _, skipPath := range skipPaths {
		if strings.HasPrefix(path, skipPath) {
			return
		}
	}

	// 获取Authorization头
	authHeader := ctx.Input.Header("Authorization")
	if authHeader == "" {
		logger.Warn("Missing authorization header", zap.String("path", path))
		ctx.Output.SetStatus(http.StatusUnauthorized)
		ctx.Output.JSON(utils.ErrorResponse(http.StatusUnauthorized, "Missing authorization header"), false, false)
		return
	}

	// 检查Bearer前缀
	if !strings.HasPrefix(authHeader, "Bearer ") {
		logger.Warn("Invalid authorization header format", zap.String("header", authHeader))
		ctx.Output.SetStatus(http.StatusUnauthorized)
		ctx.Output.JSON(utils.ErrorResponse(http.StatusUnauthorized, "Invalid authorization header format"), false, false)
		return
	}

	// 提取token
	token := strings.TrimPrefix(authHeader, "Bearer ")
	if token == "" {
		logger.Warn("Empty token", zap.String("path", path))
		ctx.Output.SetStatus(http.StatusUnauthorized)
		ctx.Output.JSON(utils.ErrorResponse(http.StatusUnauthorized, "Empty token"), false, false)
		return
	}

	// 验证token
	claims, err := utils.ParseJWT(token)
	if err != nil {
		logger.Warn("Invalid token", zap.Error(err), zap.String("path", path))
		ctx.Output.SetStatus(http.StatusUnauthorized)
		ctx.Output.JSON(utils.ErrorResponse(http.StatusUnauthorized, "Invalid token"), false, false)
		return
	}

	// 将用户信息存储到上下文
	ctx.Input.SetData("user_id", claims.UserID)
	ctx.Input.SetData("admin_name", claims.AdminName)
	ctx.Input.SetData("admin_type", claims.AdminType)
	ctx.Input.SetData("channel_id", claims.ChannelID)

	logger.Debug("User authenticated",
		zap.Int("user_id", claims.UserID),
		zap.String("admin_name", claims.AdminName),
		zap.String("path", path),
	)
}

// CORSMiddleware 跨域中间件
func CORSMiddleware(ctx *context.Context) {
	origin := ctx.Input.Header("Origin")
	if origin == "" {
		origin = "*"
	}

	ctx.Output.Header("Access-Control-Allow-Origin", origin)
	ctx.Output.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	ctx.Output.Header("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Authorization")
	ctx.Output.Header("Access-Control-Allow-Credentials", "true")
	ctx.Output.Header("Access-Control-Max-Age", "86400")

	if ctx.Input.Method() == "OPTIONS" {
		ctx.Output.SetStatus(http.StatusOK)
		return
	}
}

// LoggingMiddleware 日志中间件
func LoggingMiddleware(ctx *context.Context) {
	start := ctx.Input.GetData("start_time")
	if start == nil {
		return
	}

	// 记录请求日志
	logger.Info("Request completed",
		zap.String("method", ctx.Input.Method()),
		zap.String("path", ctx.Request.URL.Path),
		zap.String("query", ctx.Request.URL.RawQuery),
		zap.String("user_agent", ctx.Input.Header("User-Agent")),
		zap.String("ip", ctx.Input.IP()),
		zap.Int("status", ctx.Output.Status),
	)
}

// RateLimitMiddleware 限流中间件
func RateLimitMiddleware(ctx *context.Context) {
	// TODO: 实现基于Redis的限流逻辑
	// 这里可以根据IP或用户ID进行限流
}

// PermissionMiddleware 权限验证中间件
func PermissionMiddleware(ctx *context.Context) {
	// 跳过权限检查的路径
	skipPaths := []string{
		"/api/v1/auth",
		"/api/v1/user/profile",
		"/swagger",
		"/static",
		"/health",
	}

	path := ctx.Request.URL.Path
	for _, skipPath := range skipPaths {
		if strings.HasPrefix(path, skipPath) {
			return
		}
	}

	// 获取用户信息
	userID := ctx.Input.GetData("user_id")
	adminType := ctx.Input.GetData("admin_type")
	
	if userID == nil || adminType == nil {
		logger.Warn("Missing user info in context", zap.String("path", path))
		ctx.Output.SetStatus(http.StatusForbidden)
		ctx.Output.JSON(utils.ErrorResponse(http.StatusForbidden, "Access denied"), false, false)
		return
	}

	// TODO: 实现基于RBAC的权限检查逻辑
	// 1. 根据用户ID获取用户角色
	// 2. 根据角色获取权限列表
	// 3. 检查当前请求路径是否在权限列表中

	logger.Debug("Permission check passed",
		zap.Any("user_id", userID),
		zap.Any("admin_type", adminType),
		zap.String("path", path),
	)
}

// Init 初始化中间件
func Init() {
	// 注册全局中间件
	// 注意：中间件的执行顺序很重要
	
	logger.Info("Middleware initialized")
}
