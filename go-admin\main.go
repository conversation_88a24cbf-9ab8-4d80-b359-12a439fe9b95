package main

import (
	"kakabs-admin/internal/config"
	"kakabs-admin/internal/database"
	"kakabs-admin/internal/logger"
	"kakabs-admin/internal/middleware"
	"kakabs-admin/internal/router"
	"kakabs-admin/internal/scheduler"

	"github.com/beego/beego/v2/server/web"
	"go.uber.org/zap"
)

// @title Kakabs Admin API
// @version 1.0
// @description 卡卡棋牌游戏运营管理系统API文档
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host localhost:8080
// @BasePath /api/v1

// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name Authorization

func main() {
	// 初始化配置
	if err := config.Init(); err != nil {
		panic("Failed to initialize config: " + err.Error())
	}

	// 初始化日志
	if err := logger.Init(); err != nil {
		panic("Failed to initialize logger: " + err.Error())
	}
	defer logger.Sync()

	// 初始化数据库
	if err := database.Init(); err != nil {
		logger.Fatal("Failed to initialize database", zap.Error(err))
	}

	// 初始化中间件
	middleware.Init()

	// 初始化路由
	router.Init()

	// 初始化定时任务
	scheduler.Init()

	// 设置BeeGo配置
	web.BConfig.WebConfig.DirectoryIndex = true
	web.BConfig.WebConfig.StaticDir["/swagger"] = "swagger"
	web.BConfig.AppName = "kakabs-admin"
	web.BConfig.CopyRequestBody = true
	web.BConfig.RunMode = config.GetString("app.mode")
	web.BConfig.Listen.HTTPPort = config.GetInt("app.port")

	logger.Info("Starting Kakabs Admin Server",
		zap.String("mode", web.BConfig.RunMode),
		zap.Int("port", web.BConfig.Listen.HTTPPort),
	)

	// 启动服务器
	web.Run()
}
