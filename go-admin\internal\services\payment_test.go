package services

import (
	"encoding/json"
	"fmt"
	"kakabs-admin/internal/logger"
	"kakabs-admin/internal/models"
	"net/http"
	"strconv"
	"time"

	"go.uber.org/zap"
)

// PaymentTestService 支付测试服务
type PaymentTestService struct {
	Environment string
}

// NewPaymentTestService 创建支付测试服务
func NewPaymentTestService(environment string) *PaymentTestService {
	return &PaymentTestService{
		Environment: environment,
	}
}

// TestResult 测试结果
type TestResult struct {
	PayType     int                    `json:"pay_type"`
	PayTypeName string                 `json:"pay_type_name"`
	TestType    string                 `json:"test_type"`
	Success     bool                   `json:"success"`
	Message     string                 `json:"message"`
	Duration    time.Duration          `json:"duration"`
	Details     map[string]interface{} `json:"details"`
	Timestamp   time.Time              `json:"timestamp"`
}

// TestConfig 测试配置
type TestConfig struct {
	PayType     int     `json:"pay_type"`
	Environment string  `json:"environment"`
	TestAmount  float64 `json:"test_amount"`
	UserID      int     `json:"user_id"`
	ProductID   int     `json:"product_id"`
}

// TestAllPayments 测试所有支付方式
func (p *PaymentTestService) TestAllPayments(configs []TestConfig) []TestResult {
	var results []TestResult

	for _, config := range configs {
		switch config.PayType {
		case 1: // 微信支付
			result := p.testWechatPay(config)
			results = append(results, result)
		case 2: // 支付宝支付
			result := p.testAlipay(config)
			results = append(results, result)
		case 3: // 苹果内购
			result := p.testApplePay(config)
			results = append(results, result)
		case 4: // 抖音支付
			result := p.testDouyinPay(config)
			results = append(results, result)
		}
	}

	return results
}

// testWechatPay 测试微信支付
func (p *PaymentTestService) testWechatPay(config TestConfig) TestResult {
	start := time.Now()
	result := TestResult{
		PayType:     1,
		PayTypeName: "微信支付",
		TestType:    config.Environment,
		Timestamp:   start,
		Details:     make(map[string]interface{}),
	}

	// 模拟创建微信支付订单
	testOrder := &models.PaymentOrder{
		OrderID:    fmt.Sprintf("TEST_WX_%d", time.Now().Unix()),
		OutTradeNo: fmt.Sprintf("WX_TEST_%d", time.Now().Unix()),
		UserID:     config.UserID,
		ProductID:  config.ProductID,
		PayType:    1,
		PayMethod:  1, // APP支付
		TotalFee:   int(config.TestAmount * 100),
		PayMoney:   config.TestAmount,
	}

	testProduct := &models.PaymentProduct{
		Name:        "测试商品",
		Description: "微信支付测试商品",
		Price:       config.TestAmount,
	}

	// 模拟配置
	testConfig := &models.PaymentConfig{
		PayType:     1,
		AppID:       "test_wx_app_id",
		MchID:       "test_wx_mch_id",
		APIKey:      "test_wx_api_key",
		Environment: config.Environment,
	}

	// 创建微信支付服务
	wechatPay := NewWechatPayService(testConfig)

	// 测试创建订单
	_, err := wechatPay.CreateAppOrder(testOrder, testProduct)
	if err != nil {
		result.Success = false
		result.Message = fmt.Sprintf("创建微信支付订单失败: %v", err)
	} else {
		result.Success = true
		result.Message = "微信支付订单创建成功"
		result.Details["order_id"] = testOrder.OrderID
		result.Details["out_trade_no"] = testOrder.OutTradeNo
	}

	result.Duration = time.Since(start)
	return result
}

// testAlipay 测试支付宝支付
func (p *PaymentTestService) testAlipay(config TestConfig) TestResult {
	start := time.Now()
	result := TestResult{
		PayType:     2,
		PayTypeName: "支付宝支付",
		TestType:    config.Environment,
		Timestamp:   start,
		Details:     make(map[string]interface{}),
	}

	// 模拟创建支付宝支付订单
	testOrder := &models.PaymentOrder{
		OrderID:    fmt.Sprintf("TEST_ALI_%d", time.Now().Unix()),
		OutTradeNo: fmt.Sprintf("ALI_TEST_%d", time.Now().Unix()),
		UserID:     config.UserID,
		ProductID:  config.ProductID,
		PayType:    2,
		PayMethod:  1, // APP支付
		TotalFee:   int(config.TestAmount * 100),
		PayMoney:   config.TestAmount,
	}

	testProduct := &models.PaymentProduct{
		Name:        "测试商品",
		Description: "支付宝支付测试商品",
		Price:       config.TestAmount,
	}

	// 模拟配置
	testConfig := &models.PaymentConfig{
		PayType:     2,
		AppID:       "test_alipay_app_id",
		MchID:       "test_alipay_mch_id",
		APIKey:      "test_alipay_private_key",
		AppSecret:   "test_alipay_public_key",
		Environment: config.Environment,
	}

	// 创建支付宝支付服务
	alipay := NewAlipayService(testConfig)

	// 测试创建订单
	_, err := alipay.CreateAppOrder(testOrder, testProduct)
	if err != nil {
		result.Success = false
		result.Message = fmt.Sprintf("创建支付宝订单失败: %v", err)
	} else {
		result.Success = true
		result.Message = "支付宝订单创建成功"
		result.Details["order_id"] = testOrder.OrderID
		result.Details["out_trade_no"] = testOrder.OutTradeNo
	}

	result.Duration = time.Since(start)
	return result
}

// testApplePay 测试苹果内购
func (p *PaymentTestService) testApplePay(config TestConfig) TestResult {
	start := time.Now()
	result := TestResult{
		PayType:     3,
		PayTypeName: "苹果内购",
		TestType:    config.Environment,
		Timestamp:   start,
		Details:     make(map[string]interface{}),
	}

	// 创建苹果内购服务
	applePay := NewApplePayService(config.Environment)

	// 模拟收据数据（这里使用测试收据）
	testReceiptData := "test_receipt_data_base64"

	// 测试验证收据
	_, err := applePay.VerifyReceipt(testReceiptData, "")
	if err != nil {
		result.Success = false
		result.Message = fmt.Sprintf("苹果内购验证失败: %v", err)
	} else {
		result.Success = true
		result.Message = "苹果内购验证成功"
		result.Details["receipt_data"] = testReceiptData
	}

	result.Duration = time.Since(start)
	return result
}

// testDouyinPay 测试抖音支付
func (p *PaymentTestService) testDouyinPay(config TestConfig) TestResult {
	start := time.Now()
	result := TestResult{
		PayType:     4,
		PayTypeName: "抖音支付",
		TestType:    config.Environment,
		Timestamp:   start,
		Details:     make(map[string]interface{}),
	}

	// 模拟创建抖音支付订单
	testOrder := &models.PaymentOrder{
		OrderID:    fmt.Sprintf("TEST_DY_%d", time.Now().Unix()),
		OutTradeNo: fmt.Sprintf("DY_TEST_%d", time.Now().Unix()),
		UserID:     config.UserID,
		ProductID:  config.ProductID,
		PayType:    4,
		PayMethod:  1,
		TotalFee:   int(config.TestAmount * 100),
		PayMoney:   config.TestAmount,
	}

	testProduct := &models.PaymentProduct{
		Name:        "测试商品",
		Description: "抖音支付测试商品",
		Price:       config.TestAmount,
	}

	// 模拟配置
	testConfig := &models.PaymentConfig{
		PayType:     4,
		AppID:       "test_douyin_app_id",
		MchID:       "test_douyin_merchant_id",
		APIKey:      "test_douyin_private_key",
		AppSecret:   "test_douyin_public_key",
		Environment: config.Environment,
	}

	// 创建抖音支付服务
	douyinPay := NewDouyinPayService(testConfig)

	// 测试创建订单
	_, err := douyinPay.CreateOrder(testOrder, testProduct)
	if err != nil {
		result.Success = false
		result.Message = fmt.Sprintf("创建抖音支付订单失败: %v", err)
	} else {
		result.Success = true
		result.Message = "抖音支付订单创建成功"
		result.Details["order_id"] = testOrder.OrderID
		result.Details["out_trade_no"] = testOrder.OutTradeNo
	}

	result.Duration = time.Since(start)
	return result
}

// TestSmallAmountPayment 小额支付测试
func (p *PaymentTestService) TestSmallAmountPayment(payType int, amount float64) TestResult {
	config := TestConfig{
		PayType:     payType,
		Environment: "prod", // 生产环境小额测试
		TestAmount:  amount,
		UserID:      999999, // 测试用户ID
		ProductID:   1,      // 测试商品ID
	}

	switch payType {
	case 1:
		return p.testWechatPay(config)
	case 2:
		return p.testAlipay(config)
	case 3:
		return p.testApplePay(config)
	case 4:
		return p.testDouyinPay(config)
	default:
		return TestResult{
			PayType:     payType,
			PayTypeName: "未知支付方式",
			Success:     false,
			Message:     "不支持的支付方式",
			Timestamp:   time.Now(),
		}
	}
}

// GenerateTestReport 生成测试报告
func (p *PaymentTestService) GenerateTestReport(results []TestResult) string {
	report := "# 支付系统测试报告\n\n"
	report += fmt.Sprintf("测试时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	report += fmt.Sprintf("测试环境: %s\n\n", p.Environment)

	successCount := 0
	totalCount := len(results)

	report += "## 测试结果汇总\n\n"
	for _, result := range results {
		status := "❌ 失败"
		if result.Success {
			status = "✅ 成功"
			successCount++
		}

		report += fmt.Sprintf("- **%s** (%s): %s - %s (耗时: %v)\n",
			result.PayTypeName,
			result.TestType,
			status,
			result.Message,
			result.Duration,
		)
	}

	report += fmt.Sprintf("\n**成功率**: %d/%d (%.1f%%)\n\n",
		successCount, totalCount, float64(successCount)/float64(totalCount)*100)

	report += "## 详细测试结果\n\n"
	for _, result := range results {
		report += fmt.Sprintf("### %s (%s)\n", result.PayTypeName, result.TestType)
		report += fmt.Sprintf("- 状态: %t\n", result.Success)
		report += fmt.Sprintf("- 消息: %s\n", result.Message)
		report += fmt.Sprintf("- 耗时: %v\n", result.Duration)
		
		if len(result.Details) > 0 {
			report += "- 详细信息:\n"
			for k, v := range result.Details {
				report += fmt.Sprintf("  - %s: %v\n", k, v)
			}
		}
		report += "\n"
	}

	return report
}
