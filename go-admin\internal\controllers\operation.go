package controllers

import (
	"fmt"
	"kakabs-admin/internal/database"
	"kakabs-admin/internal/logger"
	"kakabs-admin/internal/models"
	"kakabs-admin/internal/utils"
	"net/http"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/server/web"
	"go.uber.org/zap"
)

// OperationController 运营管理控制器
type OperationController struct {
	web.Controller
}

// 道具类型常量
const (
	ItemTypeGold    = 1 // 金豆
	ItemTypeDiamond = 2 // 钻石
	ItemTypeTicket  = 3 // 参赛门票
	ItemTypeFuCard  = 5 // 福卡
)

// GiftItemsRequest 道具赠送请求
type GiftItemsRequest struct {
	UserIDs  []int  `json:"user_ids" valid:"Required"`  // 用户ID列表
	ItemType int    `json:"item_type" valid:"Required"` // 道具类型
	Amount   int64  `json:"amount" valid:"Required"`    // 数量
	Reason   string `json:"reason" valid:"Required"`    // 原因
}

// GiftItems 批量赠送道具
// @Title 批量赠送道具
// @Description 向指定用户批量赠送道具
// @Param body body GiftItemsRequest true "赠送信息"
// @Success 200 {object} utils.Response
// @Security ApiKeyAuth
// @router /items/gift [post]
func (c *OperationController) GiftItems() {
	var req GiftItemsRequest
	if err := c.ParseForm(&req); err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid request format")
		c.ServeJSON()
		return
	}

	// 验证道具类型
	if !isValidItemType(req.ItemType) {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid item type")
		c.ServeJSON()
		return
	}

	// 验证数量
	if req.Amount <= 0 {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Amount must be positive")
		c.ServeJSON()
		return
	}

	// 获取操作员信息
	adminName := c.Ctx.Input.GetData("admin_name").(string)

	successCount := 0
	failedUsers := []int{}

	// 批量处理每个用户
	for _, userID := range req.UserIDs {
		err := c.giftItemToUser(userID, req.ItemType, req.Amount, req.Reason, adminName)
		if err != nil {
			logger.Error("Failed to gift item to user",
				zap.Int("user_id", userID),
				zap.Int("item_type", req.ItemType),
				zap.Int64("amount", req.Amount),
				zap.Error(err))
			failedUsers = append(failedUsers, userID)
		} else {
			successCount++
		}
	}

	result := map[string]interface{}{
		"success_count": successCount,
		"failed_users":  failedUsers,
		"total_users":   len(req.UserIDs),
	}

	logger.Info("Batch gift items completed",
		zap.Int("success_count", successCount),
		zap.Int("failed_count", len(failedUsers)),
		zap.String("admin", adminName),
	)

	c.Data["json"] = utils.SuccessResponse(result)
	c.ServeJSON()
}

// giftItemToUser 向单个用户赠送道具
func (c *OperationController) giftItemToUser(userID, itemType int, amount int64, reason, adminName string) error {
	return database.Transaction(func(o orm.Ormer) error {
		// 获取用户信息
		user := models.User{}
		if err := o.QueryTable("users").Filter("user_id", userID).One(&user); err != nil {
			return fmt.Errorf("user not found: %d", userID)
		}

		// 根据道具类型更新用户资产
		var oldValue, newValue int64
		var updateField string

		switch itemType {
		case ItemTypeGold:
			oldValue = user.Gold
			newValue = oldValue + amount
			user.Gold = newValue
			updateField = "gold"

			// 记录金豆日志
			goldLog := models.UserGoldLog{
				UserID:     userID,
				Type:       1, // 收入
				Amount:     amount,
				Before:     oldValue,
				After:      newValue,
				Reason:     1, // 管理员操作
				ReasonDesc: fmt.Sprintf("管理员[%s]赠送: %s", adminName, reason),
			}
			if _, err := o.Insert(&goldLog); err != nil {
				return err
			}

		case ItemTypeDiamond:
			oldValue = user.Diamond
			newValue = oldValue + amount
			user.Diamond = newValue
			updateField = "diamond"

			// 记录钻石日志
			diamondLog := models.UserDiamondLog{
				UserID:     userID,
				Type:       1, // 收入
				Amount:     amount,
				Before:     oldValue,
				After:      newValue,
				Reason:     1, // 管理员操作
				ReasonDesc: fmt.Sprintf("管理员[%s]赠送: %s", adminName, reason),
			}
			if _, err := o.Insert(&diamondLog); err != nil {
				return err
			}

		case ItemTypeTicket:
			oldValue = int64(user.Ticket)
			newValue = oldValue + amount
			user.Ticket = int(newValue)
			updateField = "ticket"

		case ItemTypeFuCard:
			oldValue = int64(user.FuCard)
			newValue = oldValue + amount
			user.FuCard = int(newValue)
			updateField = "fu_card"
		}

		// 更新用户信息
		if _, err := o.Update(&user, updateField, "update_time"); err != nil {
			return err
		}

		return nil
	})
}

// DeductItemsRequest 道具扣除请求
type DeductItemsRequest struct {
	UserIDs  []int  `json:"user_ids" valid:"Required"`  // 用户ID列表
	ItemType int    `json:"item_type" valid:"Required"` // 道具类型
	Amount   int64  `json:"amount" valid:"Required"`    // 数量
	Reason   string `json:"reason" valid:"Required"`    // 原因
}

// DeductItems 批量扣除道具
// @Title 批量扣除道具
// @Description 从指定用户批量扣除道具
// @Param body body DeductItemsRequest true "扣除信息"
// @Success 200 {object} utils.Response
// @Security ApiKeyAuth
// @router /items/deduct [post]
func (c *OperationController) DeductItems() {
	var req DeductItemsRequest
	if err := c.ParseForm(&req); err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid request format")
		c.ServeJSON()
		return
	}

	// 验证道具类型
	if !isValidItemType(req.ItemType) {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid item type")
		c.ServeJSON()
		return
	}

	// 验证数量
	if req.Amount <= 0 {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Amount must be positive")
		c.ServeJSON()
		return
	}

	// 获取操作员信息
	adminName := c.Ctx.Input.GetData("admin_name").(string)

	successCount := 0
	failedUsers := []int{}

	// 批量处理每个用户
	for _, userID := range req.UserIDs {
		err := c.deductItemFromUser(userID, req.ItemType, req.Amount, req.Reason, adminName)
		if err != nil {
			logger.Error("Failed to deduct item from user",
				zap.Int("user_id", userID),
				zap.Int("item_type", req.ItemType),
				zap.Int64("amount", req.Amount),
				zap.Error(err))
			failedUsers = append(failedUsers, userID)
		} else {
			successCount++
		}
	}

	result := map[string]interface{}{
		"success_count": successCount,
		"failed_users":  failedUsers,
		"total_users":   len(req.UserIDs),
	}

	logger.Info("Batch deduct items completed",
		zap.Int("success_count", successCount),
		zap.Int("failed_count", len(failedUsers)),
		zap.String("admin", adminName),
	)

	c.Data["json"] = utils.SuccessResponse(result)
	c.ServeJSON()
}

// deductItemFromUser 从单个用户扣除道具
func (c *OperationController) deductItemFromUser(userID, itemType int, amount int64, reason, adminName string) error {
	return database.Transaction(func(o orm.Ormer) error {
		// 获取用户信息
		user := models.User{}
		if err := o.QueryTable("users").Filter("user_id", userID).One(&user); err != nil {
			return fmt.Errorf("user not found: %d", userID)
		}

		// 根据道具类型检查余额并扣除
		var oldValue, newValue int64
		var updateField string

		switch itemType {
		case ItemTypeGold:
			oldValue = user.Gold
			if oldValue < amount {
				return fmt.Errorf("insufficient gold balance for user %d", userID)
			}
			newValue = oldValue - amount
			user.Gold = newValue
			updateField = "gold"

			// 记录金豆日志
			goldLog := models.UserGoldLog{
				UserID:     userID,
				Type:       2, // 支出
				Amount:     amount,
				Before:     oldValue,
				After:      newValue,
				Reason:     2, // 管理员扣除
				ReasonDesc: fmt.Sprintf("管理员[%s]扣除: %s", adminName, reason),
			}
			if _, err := o.Insert(&goldLog); err != nil {
				return err
			}

		case ItemTypeDiamond:
			oldValue = user.Diamond
			if oldValue < amount {
				return fmt.Errorf("insufficient diamond balance for user %d", userID)
			}
			newValue = oldValue - amount
			user.Diamond = newValue
			updateField = "diamond"

			// 记录钻石日志
			diamondLog := models.UserDiamondLog{
				UserID:     userID,
				Type:       2, // 支出
				Amount:     amount,
				Before:     oldValue,
				After:      newValue,
				Reason:     2, // 管理员扣除
				ReasonDesc: fmt.Sprintf("管理员[%s]扣除: %s", adminName, reason),
			}
			if _, err := o.Insert(&diamondLog); err != nil {
				return err
			}

		case ItemTypeTicket:
			oldValue = int64(user.Ticket)
			if oldValue < amount {
				return fmt.Errorf("insufficient ticket balance for user %d", userID)
			}
			newValue = oldValue - amount
			user.Ticket = int(newValue)
			updateField = "ticket"

		case ItemTypeFuCard:
			oldValue = int64(user.FuCard)
			if oldValue < amount {
				return fmt.Errorf("insufficient fu_card balance for user %d", userID)
			}
			newValue = oldValue - amount
			user.FuCard = int(newValue)
			updateField = "fu_card"
		}

		// 更新用户信息
		if _, err := o.Update(&user, updateField, "update_time"); err != nil {
			return err
		}

		return nil
	})
}

// ItemLogsRequest 道具日志请求
type ItemLogsRequest struct {
	Page      int    `form:"page"`
	Size      int    `form:"size"`
	UserID    int    `form:"user_id"`
	ItemType  int    `form:"item_type"`
	Type      int    `form:"type"` // 1收入 2支出
	StartDate string `form:"start_date"`
	EndDate   string `form:"end_date"`
}

// ItemLogs 获取道具操作日志
// @Title 获取道具操作日志
// @Description 分页获取道具操作日志
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(20)
// @Param user_id query int false "用户ID"
// @Param item_type query int false "道具类型"
// @Param type query int false "操作类型"
// @Success 200 {object} utils.PageResponse
// @Security ApiKeyAuth
// @router /items/logs [get]
func (c *OperationController) ItemLogs() {
	var req ItemLogsRequest
	if err := c.ParseForm(&req); err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid request parameters")
		c.ServeJSON()
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 20
	}

	// 根据道具类型查询不同的日志表
	var logs interface{}
	var total int64
	var err error

	switch req.ItemType {
	case ItemTypeGold:
		logs, total, err = c.getGoldLogs(req)
	case ItemTypeDiamond:
		logs, total, err = c.getDiamondLogs(req)
	default:
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Unsupported item type for logs")
		c.ServeJSON()
		return
	}

	if err != nil {
		logger.Error("Failed to get item logs", zap.Error(err))
		c.Data["json"] = utils.DatabaseErrorResponse(err)
		c.ServeJSON()
		return
	}

	c.Data["json"] = utils.PageSuccessResponse(logs, total, req.Page, req.Size)
	c.ServeJSON()
}

// getGoldLogs 获取金豆日志
func (c *OperationController) getGoldLogs(req ItemLogsRequest) ([]models.UserGoldLog, int64, error) {
	o := database.GetOrm()
	qs := o.QueryTable("user_gold_log")

	// 构建查询条件
	if req.UserID > 0 {
		qs = qs.Filter("user_id", req.UserID)
	}
	if req.Type > 0 {
		qs = qs.Filter("type", req.Type)
	}
	if req.StartDate != "" {
		qs = qs.Filter("create_time__gte", req.StartDate)
	}
	if req.EndDate != "" {
		qs = qs.Filter("create_time__lte", req.EndDate)
	}

	// 获取总数
	total, err := qs.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	var logs []models.UserGoldLog
	offset := (req.Page - 1) * req.Size
	_, err = qs.OrderBy("-create_time").Limit(req.Size, offset).All(&logs)
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// getDiamondLogs 获取钻石日志
func (c *OperationController) getDiamondLogs(req ItemLogsRequest) ([]models.UserDiamondLog, int64, error) {
	o := database.GetOrm()
	qs := o.QueryTable("user_diamond_log")

	// 构建查询条件
	if req.UserID > 0 {
		qs = qs.Filter("user_id", req.UserID)
	}
	if req.Type > 0 {
		qs = qs.Filter("type", req.Type)
	}
	if req.StartDate != "" {
		qs = qs.Filter("create_time__gte", req.StartDate)
	}
	if req.EndDate != "" {
		qs = qs.Filter("create_time__lte", req.EndDate)
	}

	// 获取总数
	total, err := qs.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	var logs []models.UserDiamondLog
	offset := (req.Page - 1) * req.Size
	_, err = qs.OrderBy("-create_time").Limit(req.Size, offset).All(&logs)
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// PushMessageRequest 消息推送请求
type PushMessageRequest struct {
	Type    int    `json:"type" valid:"Required"`    // 0文字发送 1图文发送
	Title   string `json:"title" valid:"Required"`   // 推送标题
	Content string `json:"content" valid:"Required"` // 推送内容
	Target  string `json:"target"`                   // 推送目标，空表示全部用户
}

// PushMessage 推送消息
// @Title 推送消息
// @Description 向用户推送消息
// @Param body body PushMessageRequest true "推送信息"
// @Success 200 {object} utils.Response
// @Security ApiKeyAuth
// @router /push [post]
func (c *OperationController) PushMessage() {
	var req PushMessageRequest
	if err := c.ParseForm(&req); err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid request format")
		c.ServeJSON()
		return
	}

	// 获取操作员信息
	adminName := c.Ctx.Input.GetData("admin_name").(string)

	// 保存推送记录
	o := database.GetAdminOrm()
	pushRecord := models.AuroraPush{
		Type:      req.Type,
		AdminName: adminName,
		Desc:      req.Title,
		Text:      req.Content,
	}

	if _, err := o.Insert(&pushRecord); err != nil {
		logger.Error("Failed to save push record", zap.Error(err))
		c.Data["json"] = utils.DatabaseErrorResponse(err)
		c.ServeJSON()
		return
	}

	// TODO: 实际的推送逻辑
	// 这里应该调用极光推送或其他推送服务的API
	// 示例：
	// err := c.sendPushNotification(req.Title, req.Content, req.Target)
	// if err != nil {
	//     logger.Error("Failed to send push notification", zap.Error(err))
	//     c.Data["json"] = utils.ErrorResponse(http.StatusInternalServerError, "Failed to send push notification")
	//     c.ServeJSON()
	//     return
	// }

	logger.Info("Push message sent successfully",
		zap.String("title", req.Title),
		zap.String("admin", adminName),
	)

	c.Data["json"] = utils.SuccessResponse(map[string]interface{}{
		"message": "Push message sent successfully",
		"id":      pushRecord.ID,
	})
	c.ServeJSON()
}

// PushLogsRequest 推送日志请求
type PushLogsRequest struct {
	Page      int    `form:"page"`
	Size      int    `form:"size"`
	AdminName string `form:"admin_name"`
	StartDate string `form:"start_date"`
	EndDate   string `form:"end_date"`
}

// PushLogs 获取推送日志
// @Title 获取推送日志
// @Description 分页获取推送日志
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(20)
// @Param admin_name query string false "操作员名称"
// @Success 200 {object} utils.PageResponse{data=[]models.AuroraPush}
// @Security ApiKeyAuth
// @router /push/logs [get]
func (c *OperationController) PushLogs() {
	var req PushLogsRequest
	if err := c.ParseForm(&req); err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid request parameters")
		c.ServeJSON()
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 20
	}

	o := database.GetAdminOrm()
	qs := o.QueryTable("aurora_push")

	// 构建查询条件
	if req.AdminName != "" {
		qs = qs.Filter("admin_name__icontains", req.AdminName)
	}
	if req.StartDate != "" {
		qs = qs.Filter("created_at__gte", req.StartDate)
	}
	if req.EndDate != "" {
		qs = qs.Filter("created_at__lte", req.EndDate)
	}

	// 获取总数
	total, err := qs.Count()
	if err != nil {
		logger.Error("Failed to count push logs", zap.Error(err))
		c.Data["json"] = utils.DatabaseErrorResponse(err)
		c.ServeJSON()
		return
	}

	// 分页查询
	var logs []models.AuroraPush
	offset := (req.Page - 1) * req.Size
	_, err = qs.OrderBy("-created_at").Limit(req.Size, offset).All(&logs)
	if err != nil {
		logger.Error("Failed to get push logs", zap.Error(err))
		c.Data["json"] = utils.DatabaseErrorResponse(err)
		c.ServeJSON()
		return
	}

	c.Data["json"] = utils.PageSuccessResponse(logs, total, req.Page, req.Size)
	c.ServeJSON()
}

// isValidItemType 验证道具类型是否有效
func isValidItemType(itemType int) bool {
	validTypes := []int{ItemTypeGold, ItemTypeDiamond, ItemTypeTicket, ItemTypeFuCard}
	for _, t := range validTypes {
		if t == itemType {
			return true
		}
	}
	return false
}
