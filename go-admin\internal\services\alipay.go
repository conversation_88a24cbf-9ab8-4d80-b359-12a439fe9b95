package services

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"io"
	"kakabs-admin/internal/logger"
	"kakabs-admin/internal/models"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	"go.uber.org/zap"
)

// AlipayService 支付宝支付服务
type AlipayService struct {
	AppID           string
	PrivateKey      string
	AlipayPublicKey string
	NotifyURL       string
	ReturnURL       string
	Environment     string // dev/prod
}

// NewAlipayService 创建支付宝支付服务实例
func NewAlipayService(config *models.PaymentConfig) *AlipayService {
	return &AlipayService{
		AppID:           config.AppID,
		PrivateKey:      config.APIKey,
		AlipayPublicKey: config.AppSecret,
		NotifyURL:       config.NotifyURL,
		ReturnURL:       config.ReturnURL,
		Environment:     config.Environment,
	}
}

// AlipayRequest 支付宝请求结构
type AlipayRequest struct {
	AppID        string `json:"app_id"`
	Method       string `json:"method"`
	Format       string `json:"format"`
	Charset      string `json:"charset"`
	SignType     string `json:"sign_type"`
	Sign         string `json:"sign"`
	Timestamp    string `json:"timestamp"`
	Version      string `json:"version"`
	NotifyURL    string `json:"notify_url,omitempty"`
	ReturnURL    string `json:"return_url,omitempty"`
	BizContent   string `json:"biz_content"`
}

// AlipayResponse 支付宝响应结构
type AlipayResponse struct {
	Code    string `json:"code"`
	Msg     string `json:"msg"`
	SubCode string `json:"sub_code"`
	SubMsg  string `json:"sub_msg"`
}

// AlipayTradeAppPayResponse APP支付响应
type AlipayTradeAppPayResponse struct {
	AlipayResponse
	OutTradeNo string `json:"out_trade_no"`
	TradeNo    string `json:"trade_no"`
}

// CreateAppOrder 创建APP支付订单
func (a *AlipayService) CreateAppOrder(order *models.PaymentOrder, product *models.PaymentProduct) (*models.WechatPayResponse, error) {
	// 构建业务参数
	bizContent := map[string]interface{}{
		"out_trade_no":  order.OutTradeNo,
		"total_amount":  fmt.Sprintf("%.2f", order.PayMoney),
		"subject":       product.Name,
		"body":          product.Description,
		"product_code":  "QUICK_MSECURITY_PAY",
		"timeout_express": "30m",
	}

	bizContentJSON, _ := json.Marshal(bizContent)

	// 构建请求参数
	req := &AlipayRequest{
		AppID:      a.AppID,
		Method:     "alipay.trade.app.pay",
		Format:     "JSON",
		Charset:    "utf-8",
		SignType:   "RSA2",
		Timestamp:  time.Now().Format("2006-01-02 15:04:05"),
		Version:    "1.0",
		NotifyURL:  a.NotifyURL,
		BizContent: string(bizContentJSON),
	}

	// 生成签名
	sign, err := a.generateSign(req)
	if err != nil {
		return nil, fmt.Errorf("generate sign failed: %w", err)
	}
	req.Sign = sign

	// 构建支付参数字符串
	payParams := a.buildPayParams(req)

	return &models.WechatPayResponse{
		OrderID:    order.OrderID,
		OutTradeNo: order.OutTradeNo,
		PayData: map[string]interface{}{
			"pay_params": payParams,
		},
	}, nil
}

// CreateWebOrder 创建网页支付订单
func (a *AlipayService) CreateWebOrder(order *models.PaymentOrder, product *models.PaymentProduct) (string, error) {
	// 构建业务参数
	bizContent := map[string]interface{}{
		"out_trade_no":  order.OutTradeNo,
		"total_amount":  fmt.Sprintf("%.2f", order.PayMoney),
		"subject":       product.Name,
		"body":          product.Description,
		"product_code":  "FAST_INSTANT_TRADE_PAY",
	}

	bizContentJSON, _ := json.Marshal(bizContent)

	// 构建请求参数
	req := &AlipayRequest{
		AppID:     a.AppID,
		Method:    "alipay.trade.page.pay",
		Format:    "JSON",
		Charset:   "utf-8",
		SignType:  "RSA2",
		Timestamp: time.Now().Format("2006-01-02 15:04:05"),
		Version:   "1.0",
		NotifyURL: a.NotifyURL,
		ReturnURL: a.ReturnURL,
		BizContent: string(bizContentJSON),
	}

	// 生成签名
	sign, err := a.generateSign(req)
	if err != nil {
		return "", fmt.Errorf("generate sign failed: %w", err)
	}
	req.Sign = sign

	// 构建支付URL
	gateway := "https://openapi.alipay.com/gateway.do"
	if a.Environment == "dev" {
		gateway = "https://openapi.alipaydev.com/gateway.do"
	}

	payURL := gateway + "?" + a.buildPayParams(req)
	return payURL, nil
}

// VerifyNotify 验证支付回调
func (a *AlipayService) VerifyNotify(params map[string]string) error {
	// 获取签名
	sign := params["sign"]
	signType := params["sign_type"]
	delete(params, "sign")
	delete(params, "sign_type")

	// 构建待验签字符串
	signStr := a.buildSignString(params)

	// 验证签名
	return a.verifySign(signStr, sign, signType)
}

// generateSign 生成签名
func (a *AlipayService) generateSign(req *AlipayRequest) (string, error) {
	// 构建参数map
	params := map[string]string{
		"app_id":      req.AppID,
		"method":      req.Method,
		"format":      req.Format,
		"charset":     req.Charset,
		"sign_type":   req.SignType,
		"timestamp":   req.Timestamp,
		"version":     req.Version,
		"biz_content": req.BizContent,
	}

	if req.NotifyURL != "" {
		params["notify_url"] = req.NotifyURL
	}
	if req.ReturnURL != "" {
		params["return_url"] = req.ReturnURL
	}

	// 构建待签名字符串
	signStr := a.buildSignString(params)

	// RSA2签名
	return a.rsaSign(signStr)
}

// buildSignString 构建待签名字符串
func (a *AlipayService) buildSignString(params map[string]string) string {
	var keys []string
	for k := range params {
		if params[k] != "" {
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	var signParts []string
	for _, k := range keys {
		signParts = append(signParts, k+"="+params[k])
	}

	return strings.Join(signParts, "&")
}

// buildPayParams 构建支付参数字符串
func (a *AlipayService) buildPayParams(req *AlipayRequest) string {
	params := url.Values{}
	params.Set("app_id", req.AppID)
	params.Set("method", req.Method)
	params.Set("format", req.Format)
	params.Set("charset", req.Charset)
	params.Set("sign_type", req.SignType)
	params.Set("sign", req.Sign)
	params.Set("timestamp", req.Timestamp)
	params.Set("version", req.Version)
	params.Set("biz_content", req.BizContent)

	if req.NotifyURL != "" {
		params.Set("notify_url", req.NotifyURL)
	}
	if req.ReturnURL != "" {
		params.Set("return_url", req.ReturnURL)
	}

	return params.Encode()
}

// rsaSign RSA2签名
func (a *AlipayService) rsaSign(signStr string) (string, error) {
	// 解析私钥
	block, _ := pem.Decode([]byte(a.PrivateKey))
	if block == nil {
		return "", fmt.Errorf("private key decode failed")
	}

	privateKey, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return "", fmt.Errorf("parse private key failed: %w", err)
	}

	rsaPrivateKey, ok := privateKey.(*rsa.PrivateKey)
	if !ok {
		return "", fmt.Errorf("private key is not RSA key")
	}

	// SHA256哈希
	hash := sha256.Sum256([]byte(signStr))

	// RSA签名
	signature, err := rsa.SignPKCS1v15(rand.Reader, rsaPrivateKey, crypto.SHA256, hash[:])
	if err != nil {
		return "", fmt.Errorf("rsa sign failed: %w", err)
	}

	return base64.StdEncoding.EncodeToString(signature), nil
}

// verifySign 验证签名
func (a *AlipayService) verifySign(signStr, sign, signType string) error {
	if signType != "RSA2" {
		return fmt.Errorf("unsupported sign type: %s", signType)
	}

	// 解析支付宝公钥
	block, _ := pem.Decode([]byte(a.AlipayPublicKey))
	if block == nil {
		return fmt.Errorf("alipay public key decode failed")
	}

	publicKey, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return fmt.Errorf("parse alipay public key failed: %w", err)
	}

	rsaPublicKey, ok := publicKey.(*rsa.PublicKey)
	if !ok {
		return fmt.Errorf("alipay public key is not RSA key")
	}

	// 解码签名
	signature, err := base64.StdEncoding.DecodeString(sign)
	if err != nil {
		return fmt.Errorf("decode signature failed: %w", err)
	}

	// SHA256哈希
	hash := sha256.Sum256([]byte(signStr))

	// 验证签名
	err = rsa.VerifyPKCS1v15(rsaPublicKey, crypto.SHA256, hash[:], signature)
	if err != nil {
		return fmt.Errorf("verify signature failed: %w", err)
	}

	return nil
}
