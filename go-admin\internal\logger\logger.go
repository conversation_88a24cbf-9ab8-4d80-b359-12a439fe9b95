package logger

import (
	"kakabs-admin/internal/config"
	"os"
	"path/filepath"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

var logger *zap.Logger

// Init 初始化日志系统
func Init() error {
	// 创建日志目录
	logPath := config.GetString("log.output")
	if logPath == "" {
		logPath = "logs/app.log"
	}
	
	logDir := filepath.Dir(logPath)
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return err
	}

	// 配置日志轮转
	writer := &lumberjack.Logger{
		Filename:   logPath,
		MaxSize:    config.GetInt("log.max_size"),    // MB
		MaxBackups: config.GetInt("log.max_backups"), // 保留文件数
		MaxAge:     config.GetInt("log.max_age"),     // 天数
		Compress:   config.GetBool("log.compress"),   // 压缩
	}

	// 设置日志级别
	level := zapcore.InfoLevel
	switch config.GetString("log.level") {
	case "debug":
		level = zapcore.DebugLevel
	case "info":
		level = zapcore.InfoLevel
	case "warn":
		level = zapcore.WarnLevel
	case "error":
		level = zapcore.ErrorLevel
	}

	// 设置编码器
	var encoder zapcore.Encoder
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.TimeKey = "timestamp"
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder

	if config.GetString("log.format") == "console" {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	}

	// 创建核心
	core := zapcore.NewTee(
		zapcore.NewCore(encoder, zapcore.AddSync(writer), level),
		zapcore.NewCore(encoder, zapcore.AddSync(os.Stdout), level),
	)

	// 创建logger
	logger = zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))

	return nil
}

// GetLogger 获取logger实例
func GetLogger() *zap.Logger {
	return logger
}

// Sync 同步日志
func Sync() {
	if logger != nil {
		logger.Sync()
	}
}

// Debug 调试日志
func Debug(msg string, fields ...zap.Field) {
	logger.Debug(msg, fields...)
}

// Info 信息日志
func Info(msg string, fields ...zap.Field) {
	logger.Info(msg, fields...)
}

// Warn 警告日志
func Warn(msg string, fields ...zap.Field) {
	logger.Warn(msg, fields...)
}

// Error 错误日志
func Error(msg string, fields ...zap.Field) {
	logger.Error(msg, fields...)
}

// Fatal 致命错误日志
func Fatal(msg string, fields ...zap.Field) {
	logger.Fatal(msg, fields...)
}

// Panic panic日志
func Panic(msg string, fields ...zap.Field) {
	logger.Panic(msg, fields...)
}

// With 添加字段
func With(fields ...zap.Field) *zap.Logger {
	return logger.With(fields...)
}
