# 应用配置
app:
  name: "kakabs-admin"
  mode: "dev"  # dev, test, prod
  port: 8080
  debug: true

# 数据库配置
database:
  # 主数据库 - match_center
  default:
    driver: "mysql"
    host: "rm-7xv9s7iy2stpf4433xo.mysql.rds.aliyuncs.com"
    port: 3306
    username: "xy_game001"
    password: "R7VG$(YM5zH%A"
    database: "match_center"
    charset: "utf8mb4"
    max_idle_conns: 10
    max_open_conns: 100
    conn_max_lifetime: 3600

  # 管理数据库 - match_admin
  admin:
    driver: "mysql"
    host: "rm-7xv9s7iy2stpf4433xo.mysql.rds.aliyuncs.com"
    port: 3306
    username: "xy_game001"
    password: "R7VG$(YM5zH%A"
    database: "match_admin"
    charset: "utf8mb4"
    max_idle_conns: 10
    max_open_conns: 100
    conn_max_lifetime: 3600

  # 配置数据库 - match_config
  config:
    driver: "mysql"
    host: "rm-7xv9s7iy2stpf4433xo.mysql.rds.aliyuncs.com"
    port: 3306
    username: "xy_game001"
    password: "R7VG$(YM5zH%A"
    database: "match_config"
    charset: "utf8mb4"
    max_idle_conns: 10
    max_open_conns: 100
    conn_max_lifetime: 3600

  # 战队数据库 - warband_center
  warband:
    driver: "mysql"
    host: "rm-7xv9s7iy2stpf4433xo.mysql.rds.aliyuncs.com"
    port: 3306
    username: "xy_game001"
    password: "R7VG$(YM5zH%A"
    database: "warband_center"
    charset: "utf8mb4"
    max_idle_conns: 10
    max_open_conns: 100
    conn_max_lifetime: 3600

# Redis配置
redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0
  pool_size: 10

# JWT配置
jwt:
  secret: "kakabs-admin-jwt-secret-key-2024"
  expire_hours: 24
  issuer: "kakabs-admin"

# 日志配置
log:
  level: "info"  # debug, info, warn, error
  format: "json"  # json, console
  output: "logs/app.log"
  max_size: 100  # MB
  max_backups: 10
  max_age: 30  # days
  compress: true

# 文件上传配置
upload:
  path: "./uploads"
  max_size: 10485760  # 10MB
  allowed_types: ["jpg", "jpeg", "png", "gif", "pdf", "doc", "docx"]

# 支付配置
payment:
  wechat:
    app_id: ""
    mch_id: ""
    api_key: ""
    cert_path: ""
    key_path: ""
    notify_url: ""

# 消息推送配置
push:
  jpush:
    app_key: ""
    master_secret: ""

# 游戏配置
game:
  api_key: "84e10b8c99d6d9bef73e41469a816c1b"
  center_addr: "http://localhost:9001"
  
# 分享配置
share:
  base_url: "http://admin.kakabs.com"
  qr_code_path: "./uploads/qr"

# 定时任务配置
scheduler:
  enabled: true
  timezone: "Asia/Shanghai"
