package models

import (
	"time"

	"github.com/beego/beego/v2/client/orm"
)

// PaymentOrder 支付订单模型
type PaymentOrder struct {
	ID            int       `orm:"auto;pk;column(id)" json:"id"`
	OrderID       string    `orm:"size(64);unique;column(order_id)" json:"order_id"`         // 订单号
	OutTradeNo    string    `orm:"size(64);unique;column(out_trade_no)" json:"out_trade_no"` // 商户订单号
	UserID        int       `orm:"column(user_id)" json:"user_id"`                           // 用户ID
	ProductID     int       `orm:"column(product_id)" json:"product_id"`                     // 商品ID
	ProductName   string    `orm:"size(255);column(product_name)" json:"product_name"`       // 商品名称
	PayType       int       `orm:"column(pay_type)" json:"pay_type"`                         // 支付类型 1微信 2支付宝 3苹果内购
	PayMethod     int       `orm:"column(pay_method)" json:"pay_method"`                     // 支付方式 1APP 2H5 3小程序 4公众号
	TotalFee      int       `orm:"column(total_fee)" json:"total_fee"`                       // 订单金额(分)
	PayMoney      float64   `orm:"column(pay_money);default(0)" json:"pay_money"`            // 支付金额(元)
	Currency      string    `orm:"size(10);column(currency);default('CNY')" json:"currency"` // 货币类型
	Status        int       `orm:"column(status);default(0)" json:"status"`                  // 订单状态 0待支付 1已支付 2已取消 3已退款
	PayStatus     int       `orm:"column(pay_status);default(0)" json:"pay_status"`          // 支付状态 0未支付 1支付成功 2支付失败
	TransactionID string    `orm:"size(64);column(transaction_id)" json:"transaction_id"`    // 微信/支付宝交易号
	PrepayID      string    `orm:"size(64);column(prepay_id)" json:"prepay_id"`              // 预支付ID
	NotifyURL     string    `orm:"size(255);column(notify_url)" json:"notify_url"`           // 回调地址
	ReturnURL     string    `orm:"size(255);column(return_url)" json:"return_url"`           // 返回地址
	ClientIP      string    `orm:"size(50);column(client_ip)" json:"client_ip"`              // 客户端IP
	DeviceInfo    string    `orm:"size(255);column(device_info)" json:"device_info"`         // 设备信息
	Remark        string    `orm:"size(500);column(remark)" json:"remark"`                   // 备注
	PayTime       time.Time `orm:"type(datetime);null;column(pay_time)" json:"pay_time"`     // 支付时间
	ExpireTime    time.Time `orm:"type(datetime);column(expire_time)" json:"expire_time"`    // 过期时间
	CreateTime    time.Time `orm:"auto_now_add;type(datetime);column(create_time)" json:"create_time"`
	UpdateTime    time.Time `orm:"auto_now;type(datetime);column(update_time)" json:"update_time"`
}

// TableName 设置表名
func (p *PaymentOrder) TableName() string {
	return "payment_orders"
}

// PaymentConfig 支付配置模型
type PaymentConfig struct {
	ID          int       `orm:"auto;pk;column(id)" json:"id"`
	PayType     int       `orm:"column(pay_type)" json:"pay_type"`                   // 支付类型 1微信 2支付宝
	AppID       string    `orm:"size(64);column(app_id)" json:"app_id"`              // 应用ID
	MchID       string    `orm:"size(32);column(mch_id)" json:"mch_id"`              // 商户号
	APIKey      string    `orm:"size(64);column(api_key)" json:"api_key"`            // API密钥
	AppSecret   string    `orm:"size(64);column(app_secret)" json:"app_secret"`      // 应用密钥
	CertPath    string    `orm:"size(255);column(cert_path)" json:"cert_path"`       // 证书路径
	KeyPath     string    `orm:"size(255);column(key_path)" json:"key_path"`         // 私钥路径
	NotifyURL   string    `orm:"size(255);column(notify_url)" json:"notify_url"`     // 回调地址
	ReturnURL   string    `orm:"size(255);column(return_url)" json:"return_url"`     // 返回地址
	Environment string    `orm:"size(20);column(environment);default('prod')" json:"environment"` // 环境 dev/test/prod
	Status      int       `orm:"column(status);default(1)" json:"status"`           // 状态 0禁用 1启用
	CreateTime  time.Time `orm:"auto_now_add;type(datetime);column(create_time)" json:"create_time"`
	UpdateTime  time.Time `orm:"auto_now;type(datetime);column(update_time)" json:"update_time"`
}

// TableName 设置表名
func (p *PaymentConfig) TableName() string {
	return "payment_configs"
}

// PaymentProduct 支付商品模型
type PaymentProduct struct {
	ID          int       `orm:"auto;pk;column(id)" json:"id"`
	Name        string    `orm:"size(255);column(name)" json:"name"`                 // 商品名称
	Description string    `orm:"size(500);column(description)" json:"description"`   // 商品描述
	Price       float64   `orm:"column(price)" json:"price"`                         // 商品价格(元)
	OrigPrice   float64   `orm:"column(orig_price)" json:"orig_price"`               // 原价(元)
	Currency    string    `orm:"size(10);column(currency);default('CNY')" json:"currency"` // 货币类型
	ItemType    int       `orm:"column(item_type)" json:"item_type"`                 // 道具类型 1金豆 2钻石 3门票 4福卡
	ItemCount   int64     `orm:"column(item_count)" json:"item_count"`               // 道具数量
	BonusCount  int64     `orm:"column(bonus_count);default(0)" json:"bonus_count"`  // 赠送数量
	Sort        int       `orm:"column(sort);default(0)" json:"sort"`                // 排序
	Status      int       `orm:"column(status);default(1)" json:"status"`           // 状态 0下架 1上架
	CreateTime  time.Time `orm:"auto_now_add;type(datetime);column(create_time)" json:"create_time"`
	UpdateTime  time.Time `orm:"auto_now;type(datetime);column(update_time)" json:"update_time"`
}

// TableName 设置表名
func (p *PaymentProduct) TableName() string {
	return "payment_products"
}

// PaymentNotify 支付回调记录模型
type PaymentNotify struct {
	ID            int       `orm:"auto;pk;column(id)" json:"id"`
	OrderID       string    `orm:"size(64);column(order_id)" json:"order_id"`           // 订单号
	OutTradeNo    string    `orm:"size(64);column(out_trade_no)" json:"out_trade_no"`   // 商户订单号
	TransactionID string    `orm:"size(64);column(transaction_id)" json:"transaction_id"` // 第三方交易号
	PayType       int       `orm:"column(pay_type)" json:"pay_type"`                    // 支付类型
	NotifyType    string    `orm:"size(50);column(notify_type)" json:"notify_type"`     // 通知类型
	NotifyData    string    `orm:"type(text);column(notify_data)" json:"notify_data"`   // 通知数据
	ProcessStatus int       `orm:"column(process_status);default(0)" json:"process_status"` // 处理状态 0未处理 1已处理 2处理失败
	ProcessResult string    `orm:"type(text);column(process_result)" json:"process_result"` // 处理结果
	ProcessTime   time.Time `orm:"type(datetime);null;column(process_time)" json:"process_time"` // 处理时间
	CreateTime    time.Time `orm:"auto_now_add;type(datetime);column(create_time)" json:"create_time"`
}

// TableName 设置表名
func (p *PaymentNotify) TableName() string {
	return "payment_notifies"
}

// PaymentRefund 退款记录模型
type PaymentRefund struct {
	ID            int       `orm:"auto;pk;column(id)" json:"id"`
	RefundID      string    `orm:"size(64);unique;column(refund_id)" json:"refund_id"`     // 退款单号
	OrderID       string    `orm:"size(64);column(order_id)" json:"order_id"`              // 原订单号
	OutTradeNo    string    `orm:"size(64);column(out_trade_no)" json:"out_trade_no"`      // 商户订单号
	TransactionID string    `orm:"size(64);column(transaction_id)" json:"transaction_id"`  // 第三方交易号
	RefundFee     int       `orm:"column(refund_fee)" json:"refund_fee"`                   // 退款金额(分)
	TotalFee      int       `orm:"column(total_fee)" json:"total_fee"`                     // 订单总金额(分)
	RefundReason  string    `orm:"size(255);column(refund_reason)" json:"refund_reason"`   // 退款原因
	RefundStatus  int       `orm:"column(refund_status);default(0)" json:"refund_status"`  // 退款状态 0申请中 1成功 2失败
	RefundTime    time.Time `orm:"type(datetime);null;column(refund_time)" json:"refund_time"` // 退款时间
	AdminID       int       `orm:"column(admin_id)" json:"admin_id"`                       // 操作管理员ID
	AdminName     string    `orm:"size(50);column(admin_name)" json:"admin_name"`          // 操作管理员名称
	CreateTime    time.Time `orm:"auto_now_add;type(datetime);column(create_time)" json:"create_time"`
	UpdateTime    time.Time `orm:"auto_now;type(datetime);column(update_time)" json:"update_time"`
}

// TableName 设置表名
func (p *PaymentRefund) TableName() string {
	return "payment_refunds"
}

// PaymentStats 支付统计数据结构
type PaymentStats struct {
	TodayOrders    int     `json:"today_orders"`    // 今日订单数
	TodayRevenue   float64 `json:"today_revenue"`   // 今日收入
	TodayPayUsers  int     `json:"today_pay_users"` // 今日付费用户数
	MonthOrders    int     `json:"month_orders"`    // 本月订单数
	MonthRevenue   float64 `json:"month_revenue"`   // 本月收入
	MonthPayUsers  int     `json:"month_pay_users"` // 本月付费用户数
	TotalOrders    int     `json:"total_orders"`    // 总订单数
	TotalRevenue   float64 `json:"total_revenue"`   // 总收入
	SuccessRate    float64 `json:"success_rate"`    // 支付成功率
	RefundRate     float64 `json:"refund_rate"`     // 退款率
}

// WechatPayRequest 微信支付请求结构
type WechatPayRequest struct {
	UserID    int    `json:"user_id" valid:"Required"`
	ProductID int    `json:"product_id" valid:"Required"`
	PayMethod int    `json:"pay_method" valid:"Required"` // 1APP 2H5 3小程序 4公众号
	ClientIP  string `json:"client_ip"`
	OpenID    string `json:"open_id"` // 微信用户openid(小程序/公众号支付必填)
}

// WechatPayResponse 微信支付响应结构
type WechatPayResponse struct {
	OrderID    string                 `json:"order_id"`
	OutTradeNo string                 `json:"out_trade_no"`
	PayData    map[string]interface{} `json:"pay_data"` // 支付参数
}

// PaymentOrderQuery 支付订单查询请求
type PaymentOrderQuery struct {
	Page       int    `form:"page"`
	Size       int    `form:"size"`
	OrderID    string `form:"order_id"`
	OutTradeNo string `form:"out_trade_no"`
	UserID     int    `form:"user_id"`
	PayType    int    `form:"pay_type"`
	Status     int    `form:"status"`
	StartDate  string `form:"start_date"`
	EndDate    string `form:"end_date"`
}

// RefundRequest 退款请求结构
type RefundRequest struct {
	OrderID      string `json:"order_id" valid:"Required"`
	RefundFee    int    `json:"refund_fee" valid:"Required"` // 退款金额(分)
	RefundReason string `json:"refund_reason" valid:"Required"`
}

func init() {
	// 注册支付相关模型
	orm.RegisterModelWithPrefix("", new(PaymentOrder))
	orm.RegisterModelWithPrefix("", new(PaymentConfig))
	orm.RegisterModelWithPrefix("", new(PaymentProduct))
	orm.RegisterModelWithPrefix("", new(PaymentNotify))
	orm.RegisterModelWithPrefix("", new(PaymentRefund))
}
