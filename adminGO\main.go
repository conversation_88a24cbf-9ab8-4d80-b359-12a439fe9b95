package main

import (
	"fmt"
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
	"e:\study\admin.kakabs.com\adminGO\internal\retention"
)

func main() {
	// 初始化路由
	r := gin.Default()

	// 留存率相关路由
	r.GET("/api/retention/daily", retention.GetDailyRetention)
	r.GET("/api/retention/3days", retention.Get3DaysRetention)
	r.GET("/api/retention/7days", retention.Get7DaysRetention)
	r.GET("/api/retention/15days", retention.Get15DaysRetention)
	r.GET("/api/retention/30days", retention.Get30DaysRetention)
	// 获取留存用户列表路由
	r.GET("/api/retention/users", retention.GetRetentionUsersHandler)

	// 启动服务器
	fmt.Println("Server is running on http://localhost:8080")
	if err := r.Run(":8080"); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}