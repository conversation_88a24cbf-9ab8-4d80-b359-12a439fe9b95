package controllers

import (
	"kakabs-admin/internal/database"
	"kakabs-admin/internal/logger"
	"kakabs-admin/internal/models"
	"kakabs-admin/internal/utils"
	"net/http"
	"strconv"
	"time"

	"github.com/beego/beego/v2/server/web"
	"go.uber.org/zap"
)

// UserController 用户管理控制器
type UserController struct {
	web.Controller
}

// UserListRequest 用户列表请求
type UserListRequest struct {
	Page     int    `form:"page"`
	Size     int    `form:"size"`
	Keyword  string `form:"keyword"`
	Level    int    `form:"level"`
	Status   int    `form:"status"`
	ParentID int    `form:"parent_id"`
}

// List 获取用户列表
// @Title 获取用户列表
// @Description 分页获取用户列表
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(20)
// @Param keyword query string false "搜索关键词"
// @Param level query int false "用户等级"
// @Param status query int false "用户状态"
// @Success 200 {object} utils.PageResponse{data=[]models.User}
// @Security ApiKeyAuth
// @router / [get]
func (c *UserController) List() {
	var req UserListRequest
	if err := c.ParseForm(&req); err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid request parameters")
		c.ServeJSON()
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 20
	}

	o := database.GetOrm()
	qs := o.QueryTable("users")

	// 构建查询条件
	if req.Keyword != "" {
		qs = qs.Filter("username__icontains", req.Keyword).
			Or("nickname__icontains", req.Keyword).
			Or("phone__icontains", req.Keyword)
	}
	if req.Level > 0 {
		qs = qs.Filter("level", req.Level)
	}
	if req.Status >= 0 {
		qs = qs.Filter("status", req.Status)
	}
	if req.ParentID > 0 {
		qs = qs.Filter("parent_id", req.ParentID)
	}

	// 获取总数
	total, err := qs.Count()
	if err != nil {
		logger.Error("Failed to count users", zap.Error(err))
		c.Data["json"] = utils.DatabaseErrorResponse(err)
		c.ServeJSON()
		return
	}

	// 分页查询
	var users []models.User
	offset := (req.Page - 1) * req.Size
	_, err = qs.OrderBy("-register_time").Limit(req.Size, offset).All(&users)
	if err != nil {
		logger.Error("Failed to get users", zap.Error(err))
		c.Data["json"] = utils.DatabaseErrorResponse(err)
		c.ServeJSON()
		return
	}

	c.Data["json"] = utils.PageSuccessResponse(users, total, req.Page, req.Size)
	c.ServeJSON()
}

// Get 获取用户详情
// @Title 获取用户详情
// @Description 根据ID获取用户详细信息
// @Param id path int true "用户ID"
// @Success 200 {object} utils.Response{data=models.User}
// @Failure 404 {object} utils.Response
// @Security ApiKeyAuth
// @router /:id [get]
func (c *UserController) Get() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid user ID")
		c.ServeJSON()
		return
	}

	o := database.GetOrm()
	user := models.User{}
	
	err = o.QueryTable("users").Filter("user_id", id).One(&user)
	if err != nil {
		logger.Error("Failed to get user", zap.Int("user_id", id), zap.Error(err))
		c.Data["json"] = utils.ResourceNotFoundResponse("User")
		c.ServeJSON()
		return
	}

	c.Data["json"] = utils.SuccessResponse(user)
	c.ServeJSON()
}

// UpdateGoldRequest 更新金豆请求
type UpdateGoldRequest struct {
	Amount int64  `json:"amount" valid:"Required"`
	Reason string `json:"reason" valid:"Required"`
}

// UpdateGold 更新用户金豆
// @Title 更新用户金豆
// @Description 增加或减少用户金豆
// @Param id path int true "用户ID"
// @Param body body UpdateGoldRequest true "金豆信息"
// @Success 200 {object} utils.Response
// @Security ApiKeyAuth
// @router /:id/gold [post]
func (c *UserController) UpdateGold() {
	idStr := c.Ctx.Input.Param(":id")
	userID, err := strconv.Atoi(idStr)
	if err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid user ID")
		c.ServeJSON()
		return
	}

	var req UpdateGoldRequest
	if err := c.ParseForm(&req); err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid request format")
		c.ServeJSON()
		return
	}

	// 获取操作员信息
	adminName := c.Ctx.Input.GetData("admin_name").(string)

	// 执行事务
	err = database.Transaction(func(o orm.Ormer) error {
		// 获取用户当前信息
		user := models.User{}
		if err := o.QueryTable("users").Filter("user_id", userID).One(&user); err != nil {
			return err
		}

		// 计算新的金豆数量
		oldGold := user.Gold
		newGold := oldGold + req.Amount

		if newGold < 0 {
			return fmt.Errorf("insufficient gold balance")
		}

		// 更新用户金豆
		user.Gold = newGold
		if _, err := o.Update(&user, "gold", "update_time"); err != nil {
			return err
		}

		// 记录金豆变动日志
		goldLog := models.UserGoldLog{
			UserID:     userID,
			Type:       1, // 1收入 2支出
			Amount:     req.Amount,
			Before:     oldGold,
			After:      newGold,
			Reason:     1, // 管理员操作
			ReasonDesc: fmt.Sprintf("管理员[%s]操作: %s", adminName, req.Reason),
		}

		if req.Amount < 0 {
			goldLog.Type = 2
			goldLog.Amount = -req.Amount
		}

		if _, err := o.Insert(&goldLog); err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		logger.Error("Failed to update user gold", 
			zap.Int("user_id", userID), 
			zap.Int64("amount", req.Amount),
			zap.Error(err))
		c.Data["json"] = utils.ErrorResponse(http.StatusInternalServerError, err.Error())
		c.ServeJSON()
		return
	}

	logger.Info("User gold updated successfully",
		zap.Int("user_id", userID),
		zap.Int64("amount", req.Amount),
		zap.String("admin", adminName),
	)

	c.Data["json"] = utils.SuccessResponse(nil)
	c.ServeJSON()
}

// UpdateDiamondRequest 更新钻石请求
type UpdateDiamondRequest struct {
	Amount int64  `json:"amount" valid:"Required"`
	Reason string `json:"reason" valid:"Required"`
}

// UpdateDiamond 更新用户钻石
// @Title 更新用户钻石
// @Description 增加或减少用户钻石
// @Param id path int true "用户ID"
// @Param body body UpdateDiamondRequest true "钻石信息"
// @Success 200 {object} utils.Response
// @Security ApiKeyAuth
// @router /:id/diamond [post]
func (c *UserController) UpdateDiamond() {
	idStr := c.Ctx.Input.Param(":id")
	userID, err := strconv.Atoi(idStr)
	if err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid user ID")
		c.ServeJSON()
		return
	}

	var req UpdateDiamondRequest
	if err := c.ParseForm(&req); err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid request format")
		c.ServeJSON()
		return
	}

	// 获取操作员信息
	adminName := c.Ctx.Input.GetData("admin_name").(string)

	// 执行事务
	err = database.Transaction(func(o orm.Ormer) error {
		// 获取用户当前信息
		user := models.User{}
		if err := o.QueryTable("users").Filter("user_id", userID).One(&user); err != nil {
			return err
		}

		// 计算新的钻石数量
		oldDiamond := user.Diamond
		newDiamond := oldDiamond + req.Amount

		if newDiamond < 0 {
			return fmt.Errorf("insufficient diamond balance")
		}

		// 更新用户钻石
		user.Diamond = newDiamond
		if _, err := o.Update(&user, "diamond", "update_time"); err != nil {
			return err
		}

		// 记录钻石变动日志
		diamondLog := models.UserDiamondLog{
			UserID:     userID,
			Type:       1, // 1收入 2支出
			Amount:     req.Amount,
			Before:     oldDiamond,
			After:      newDiamond,
			Reason:     1, // 管理员操作
			ReasonDesc: fmt.Sprintf("管理员[%s]操作: %s", adminName, req.Reason),
		}

		if req.Amount < 0 {
			diamondLog.Type = 2
			diamondLog.Amount = -req.Amount
		}

		if _, err := o.Insert(&diamondLog); err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		logger.Error("Failed to update user diamond", 
			zap.Int("user_id", userID), 
			zap.Int64("amount", req.Amount),
			zap.Error(err))
		c.Data["json"] = utils.ErrorResponse(http.StatusInternalServerError, err.Error())
		c.ServeJSON()
		return
	}

	logger.Info("User diamond updated successfully",
		zap.Int("user_id", userID),
		zap.Int64("amount", req.Amount),
		zap.String("admin", adminName),
	)

	c.Data["json"] = utils.SuccessResponse(nil)
	c.ServeJSON()
}

// BanRequest 封号请求
type BanRequest struct {
	Reason string `json:"reason" valid:"Required"`
}

// Ban 封禁用户
// @Title 封禁用户
// @Description 封禁指定用户
// @Param id path int true "用户ID"
// @Param body body BanRequest true "封禁原因"
// @Success 200 {object} utils.Response
// @Security ApiKeyAuth
// @router /:id/ban [post]
func (c *UserController) Ban() {
	idStr := c.Ctx.Input.Param(":id")
	userID, err := strconv.Atoi(idStr)
	if err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid user ID")
		c.ServeJSON()
		return
	}

	var req BanRequest
	if err := c.ParseForm(&req); err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid request format")
		c.ServeJSON()
		return
	}

	// 获取操作员信息
	adminID := c.Ctx.Input.GetData("user_id").(int)
	adminName := c.Ctx.Input.GetData("admin_name").(string)

	// 执行事务
	err = database.Transaction(func(o orm.Ormer) error {
		// 更新用户状态
		user := models.User{}
		if err := o.QueryTable("users").Filter("user_id", userID).One(&user); err != nil {
			return err
		}

		user.Status = 0 // 0表示封号
		if _, err := o.Update(&user, "status", "update_time"); err != nil {
			return err
		}

		// 记录封号日志
		banLog := models.UserBanLog{
			UserID:    userID,
			Type:      1, // 1封号
			Reason:    req.Reason,
			AdminID:   adminID,
			AdminName: adminName,
		}

		if _, err := o.Insert(&banLog); err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		logger.Error("Failed to ban user", zap.Int("user_id", userID), zap.Error(err))
		c.Data["json"] = utils.DatabaseErrorResponse(err)
		c.ServeJSON()
		return
	}

	logger.Info("User banned successfully",
		zap.Int("user_id", userID),
		zap.String("reason", req.Reason),
		zap.String("admin", adminName),
	)

	c.Data["json"] = utils.SuccessResponse(nil)
	c.ServeJSON()
}

// Unban 解封用户
// @Title 解封用户
// @Description 解封指定用户
// @Param id path int true "用户ID"
// @Success 200 {object} utils.Response
// @Security ApiKeyAuth
// @router /:id/unban [post]
func (c *UserController) Unban() {
	idStr := c.Ctx.Input.Param(":id")
	userID, err := strconv.Atoi(idStr)
	if err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid user ID")
		c.ServeJSON()
		return
	}

	// 获取操作员信息
	adminID := c.Ctx.Input.GetData("user_id").(int)
	adminName := c.Ctx.Input.GetData("admin_name").(string)

	// 执行事务
	err = database.Transaction(func(o orm.Ormer) error {
		// 更新用户状态
		user := models.User{}
		if err := o.QueryTable("users").Filter("user_id", userID).One(&user); err != nil {
			return err
		}

		user.Status = 1 // 1表示正常
		if _, err := o.Update(&user, "status", "update_time"); err != nil {
			return err
		}

		// 记录解封日志
		banLog := models.UserBanLog{
			UserID:    userID,
			Type:      2, // 2解封
			Reason:    "管理员解封",
			AdminID:   adminID,
			AdminName: adminName,
		}

		if _, err := o.Insert(&banLog); err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		logger.Error("Failed to unban user", zap.Int("user_id", userID), zap.Error(err))
		c.Data["json"] = utils.DatabaseErrorResponse(err)
		c.ServeJSON()
		return
	}

	logger.Info("User unbanned successfully",
		zap.Int("user_id", userID),
		zap.String("admin", adminName),
	)

	c.Data["json"] = utils.SuccessResponse(nil)
	c.ServeJSON()
}
