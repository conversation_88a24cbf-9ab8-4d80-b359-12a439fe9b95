# Kakabs Admin - Go版本游戏运营管理系统

## 项目简介

这是将原PHP版本的卡卡棋牌游戏运营管理系统转换为Go语言版本的项目。使用现代化的Go技术栈，提供高性能、高并发的游戏运营管理解决方案。

## 技术栈

- **Web框架**: BeeGo v2.2.1
- **ORM**: BeeGo ORM
- **配置管理**: Viper
- **日志系统**: Zap
- **认证授权**: JWT
- **API文档**: Swagger
- **定时任务**: Cron v3
- **缓存**: Redis
- **数据库**: MySQL 8.0

## 项目结构

```
go-admin/
├── main.go                 # 应用入口
├── go.mod                  # Go模块文件
├── conf/                   # 配置文件
│   └── app.yaml           # 应用配置
├── internal/              # 内部包
│   ├── config/            # 配置管理
│   ├── database/          # 数据库连接
│   ├── logger/            # 日志系统
│   ├── middleware/        # 中间件
│   ├── models/            # 数据模型
│   ├── controllers/       # 控制器
│   ├── router/            # 路由配置
│   ├── scheduler/         # 定时任务
│   └── utils/             # 工具函数
├── docs/                  # API文档
├── logs/                  # 日志文件
└── uploads/               # 上传文件
```

## 核心功能模块

### 1. 用户管理系统
- 用户列表查询和管理
- 用户封号/解封
- 用户资产管理（金豆、钻石、门票、福卡）
- 用户关系管理（代理商体系）

### 2. 运营管理系统
- 道具批量赠送/扣除
- 消息推送管理
- 公告管理
- 功能开关控制

### 3. 数据统计系统
- 实时在线用户统计
- 收入数据统计
- 用户活跃度分析
- 游戏数据汇总

### 4. 权限管理系统
- 基于RBAC的权限控制
- 角色管理
- 管理员管理
- JWT认证

### 5. 支付管理系统
- 订单管理
- 支付配置
- 退款处理
- 支付统计

### 6. 游戏管理系统
- 比赛管理
- 游戏配置
- 机器人管理
- 游戏控制

## 安装和运行

### 1. 环境要求
- Go 1.21+
- MySQL 8.0+
- Redis 6.0+

### 2. 安装依赖
```bash
cd go-admin
go mod tidy
```

### 3. 配置数据库
修改 `conf/app.yaml` 中的数据库配置：
```yaml
database:
  default:
    host: "your-mysql-host"
    port: 3306
    username: "your-username"
    password: "your-password"
    database: "match_center"
```

### 4. 运行应用
```bash
# 开发模式
go run main.go

# 编译运行
go build -o kakabs-admin main.go
./kakabs-admin
```

### 5. 访问应用
- API文档: http://localhost:8080/swagger/
- 健康检查: http://localhost:8080/health

## API接口

### 认证相关
- `POST /api/v1/auth/login` - 管理员登录
- `POST /api/v1/auth/logout` - 管理员登出
- `GET /api/v1/auth/profile` - 获取用户信息
- `POST /api/v1/auth/change-password` - 修改密码

### 用户管理
- `GET /api/v1/users` - 获取用户列表
- `GET /api/v1/users/:id` - 获取用户详情
- `POST /api/v1/users/:id/gold` - 更新用户金豆
- `POST /api/v1/users/:id/diamond` - 更新用户钻石
- `POST /api/v1/users/:id/ban` - 封禁用户
- `POST /api/v1/users/:id/unban` - 解封用户

### 运营管理
- `POST /api/v1/operation/items/gift` - 批量赠送道具
- `POST /api/v1/operation/items/deduct` - 批量扣除道具
- `GET /api/v1/operation/items/logs` - 获取道具日志
- `POST /api/v1/operation/push` - 推送消息
- `GET /api/v1/operation/push/logs` - 获取推送日志

## 性能优势

相比PHP版本，Go版本具有以下优势：

1. **性能提升**: 5-10倍的处理性能提升
2. **并发能力**: 支持数万级并发连接
3. **内存效率**: 内存使用减少50-80%
4. **部署简单**: 单一二进制文件，无需运行时环境
5. **类型安全**: 编译时类型检查，减少运行时错误

## 开发指南

### 1. 添加新的API接口
1. 在 `internal/controllers/` 中创建控制器
2. 在 `internal/router/router.go` 中注册路由
3. 添加Swagger注释生成API文档

### 2. 添加新的数据模型
1. 在 `internal/models/` 中定义模型结构
2. 在模型的 `init()` 函数中注册到ORM
3. 运行数据库迁移

### 3. 添加定时任务
1. 在 `internal/scheduler/scheduler.go` 中添加任务函数
2. 在 `addJobs()` 函数中注册定时任务

## 部署指南

### 1. Docker部署
```dockerfile
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go mod tidy && go build -o kakabs-admin main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/kakabs-admin .
COPY --from=builder /app/conf ./conf
CMD ["./kakabs-admin"]
```

### 2. 系统服务部署
```bash
# 创建系统服务文件
sudo tee /etc/systemd/system/kakabs-admin.service > /dev/null <<EOF
[Unit]
Description=Kakabs Admin Service
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/opt/kakabs-admin
ExecStart=/opt/kakabs-admin/kakabs-admin
Restart=always

[Install]
WantedBy=multi-user.target
EOF

# 启动服务
sudo systemctl enable kakabs-admin
sudo systemctl start kakabs-admin
```

## 监控和日志

### 1. 日志配置
日志文件位于 `logs/` 目录，支持自动轮转和压缩。

### 2. 健康检查
访问 `/health` 端点进行健康检查。

### 3. 性能监控
可以集成Prometheus和Grafana进行性能监控。

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

本项目采用MIT许可证。
