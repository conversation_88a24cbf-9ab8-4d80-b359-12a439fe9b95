package controllers

import (
	"kakabs-admin/internal/database"
	"kakabs-admin/internal/logger"
	"kakabs-admin/internal/models"
	"kakabs-admin/internal/utils"
	"net/http"
	"strconv"
	"time"

	"github.com/beego/beego/v2/server/web"
	"go.uber.org/zap"
)

// StatisticsController 数据统计控制器
type StatisticsController struct {
	web.Controller
}

// Realtime 获取实时统计数据
// @Title 获取实时统计数据
// @Description 获取当前实时的统计数据，包括在线用户、今日数据等
// @Success 200 {object} utils.Response{data=models.RealtimeStats}
// @Security ApiKeyAuth
// @router /realtime [get]
func (c *StatisticsController) Realtime() {
	// 获取今日时间范围
	todayStart := time.Now().Truncate(24 * time.Hour)
	todayEnd := todayStart.Add(24 * time.Hour)

	o := database.GetOrm()
	adminO := database.GetAdminOrm()

	var stats models.RealtimeStats

	// 获取今日新增用户数
	todayNewCount, err := o.QueryTable("users").
		Filter("register_time__gte", todayStart).
		Filter("register_time__lt", todayEnd).
		Filter("status", 1).
		Count()
	if err != nil {
		logger.Error("Failed to get today new users", zap.Error(err))
	}
	stats.TodayNewUsers = int(todayNewCount)

	// 获取今日活跃用户数（登录过的用户）
	sql := "SELECT COUNT(DISTINCT user_id) as count FROM user_login_log WHERE login_time >= ? AND login_time < ?"
	var activeResult []orm.Params
	_, err = o.Raw(sql, todayStart, todayEnd).Values(&activeResult)
	if err != nil {
		logger.Error("Failed to get today active users", zap.Error(err))
	} else if len(activeResult) > 0 {
		if count, ok := activeResult[0]["count"].(string); ok {
			if activeCount, err := strconv.Atoi(count); err == nil {
				stats.TodayActive = activeCount
			}
		}
	}

	// 获取今日收入
	var incomeResult []orm.Params
	incomeSql := "SELECT SUM(pay_money) as revenue FROM pay_log WHERE log_date >= ? AND log_date < ? AND check_pay_state = 1"
	_, err = o.Raw(incomeSql, todayStart, todayEnd).Values(&incomeResult)
	if err != nil {
		logger.Error("Failed to get today revenue", zap.Error(err))
	} else if len(incomeResult) > 0 {
		if revenue, ok := incomeResult[0]["revenue"].(string); ok {
			if revenueFloat, err := strconv.ParseFloat(revenue, 64); err == nil {
				stats.TodayRevenue = revenueFloat
			}
		}
	}

	// 获取今日最高在线人数
	var maxOnlineResult []orm.Params
	maxOnlineSql := "SELECT MAX(all_people_num) as max_online FROM also_online_log WHERE view_time >= ? AND view_time < ?"
	_, err = adminO.Raw(maxOnlineSql, todayStart, todayEnd).Values(&maxOnlineResult)
	if err != nil {
		logger.Error("Failed to get today max online", zap.Error(err))
	} else if len(maxOnlineResult) > 0 {
		if maxOnline, ok := maxOnlineResult[0]["max_online"].(string); ok {
			if maxOnlineInt, err := strconv.Atoi(maxOnline); err == nil {
				stats.TodayMaxOnline = maxOnlineInt
			}
		}
	}

	// 获取当前在线用户数（从最新的在线日志记录）
	var latestOnlineResult []orm.Params
	latestOnlineSql := "SELECT all_people_num FROM also_online_log ORDER BY view_time DESC LIMIT 1"
	_, err = adminO.Raw(latestOnlineSql).Values(&latestOnlineResult)
	if err != nil {
		logger.Error("Failed to get current online users", zap.Error(err))
	} else if len(latestOnlineResult) > 0 {
		if onlineUsers, ok := latestOnlineResult[0]["all_people_num"].(string); ok {
			if onlineInt, err := strconv.Atoi(onlineUsers); err == nil {
				stats.OnlineUsers = onlineInt
			}
		}
	}

	// TODO: 从游戏中心API获取实时在线数据
	// stats.GameCenterOnline = getGameCenterOnline()
	// stats.GameHouseOnline = getGameHouseOnline()

	c.Data["json"] = utils.SuccessResponse(stats)
	c.ServeJSON()
}

// OnlineStats 获取在线用户统计
// @Title 获取在线用户统计
// @Description 获取在线用户的详细统计数据
// @Param hours query int false "统计小时数" default(24)
// @Success 200 {object} utils.Response{data=models.OnlineChart}
// @Security ApiKeyAuth
// @router /online [get]
func (c *StatisticsController) OnlineStats() {
	hoursStr := c.GetString("hours", "24")
	hours, err := strconv.Atoi(hoursStr)
	if err != nil || hours <= 0 {
		hours = 24
	}

	// 计算时间范围
	endTime := time.Now()
	startTime := endTime.Add(-time.Duration(hours) * time.Hour)

	adminO := database.GetAdminOrm()

	// 获取在线用户数据
	var onlineData []models.OnlineStats
	_, err = adminO.QueryTable("also_online_log").
		Filter("view_time__gte", startTime).
		Filter("view_time__lte", endTime).
		OrderBy("view_time").
		All(&onlineData)

	if err != nil {
		logger.Error("Failed to get online stats", zap.Error(err))
		c.Data["json"] = utils.DatabaseErrorResponse(err)
		c.ServeJSON()
		return
	}

	// 构建图表数据
	var chart models.OnlineChart
	for _, data := range onlineData {
		chart.Times = append(chart.Times, data.ViewTime.Format("15:04"))
		chart.OnlineUsers = append(chart.OnlineUsers, data.AllPeopleNum)
	}

	c.Data["json"] = utils.SuccessResponse(chart)
	c.ServeJSON()
}

// DailyUsersRequest 日用户统计请求
type DailyUsersRequest struct {
	StartDate string `form:"start_date"`
	EndDate   string `form:"end_date"`
	Days      int    `form:"days"`
}

// DailyUsers 获取日用户统计
// @Title 获取日用户统计
// @Description 获取指定时间范围内的日用户统计数据
// @Param start_date query string false "开始日期 YYYY-MM-DD"
// @Param end_date query string false "结束日期 YYYY-MM-DD"
// @Param days query int false "统计天数" default(30)
// @Success 200 {object} utils.Response{data=models.UserActivityChart}
// @Security ApiKeyAuth
// @router /users/daily [get]
func (c *StatisticsController) DailyUsers() {
	var req DailyUsersRequest
	if err := c.ParseForm(&req); err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid request parameters")
		c.ServeJSON()
		return
	}

	// 设置默认值
	if req.Days <= 0 {
		req.Days = 30
	}

	var startTime, endTime time.Time
	var err error

	if req.StartDate != "" && req.EndDate != "" {
		startTime, err = time.Parse("2006-01-02", req.StartDate)
		if err != nil {
			c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid start_date format")
			c.ServeJSON()
			return
		}
		endTime, err = time.Parse("2006-01-02", req.EndDate)
		if err != nil {
			c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid end_date format")
			c.ServeJSON()
			return
		}
		endTime = endTime.Add(24 * time.Hour) // 包含结束日期
	} else {
		endTime = time.Now().Truncate(24 * time.Hour).Add(24 * time.Hour)
		startTime = endTime.Add(-time.Duration(req.Days) * 24 * time.Hour)
	}

	adminO := database.GetAdminOrm()

	// 获取日活跃数据
	var dailyData []models.DayActive
	_, err = adminO.QueryTable("day_active").
		Filter("time__gte", startTime.Format("2006-01-02")).
		Filter("time__lt", endTime.Format("2006-01-02")).
		OrderBy("time").
		All(&dailyData)

	if err != nil {
		logger.Error("Failed to get daily users", zap.Error(err))
		c.Data["json"] = utils.DatabaseErrorResponse(err)
		c.ServeJSON()
		return
	}

	// 构建图表数据
	var chart models.UserActivityChart
	for _, data := range dailyData {
		chart.Dates = append(chart.Dates, data.Time)
		chart.NewUsers = append(chart.NewUsers, data.NewAddNum)
		chart.ActiveUsers = append(chart.ActiveUsers, data.UserActiveNum)
	}

	c.Data["json"] = utils.SuccessResponse(chart)
	c.ServeJSON()
}

// UserRetention 获取用户留存统计
// @Title 获取用户留存统计
// @Description 获取用户留存率统计数据
// @Param days query int false "统计天数" default(30)
// @Success 200 {object} utils.Response{data=[]models.UserRetention}
// @Security ApiKeyAuth
// @router /users/retention [get]
func (c *StatisticsController) UserRetention() {
	daysStr := c.GetString("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days <= 0 {
		days = 30
	}

	// 计算时间范围
	endDate := time.Now().Format("2006-01-02")
	startDate := time.Now().AddDate(0, 0, -days).Format("2006-01-02")

	adminO := database.GetAdminOrm()

	// 获取用户留存数据
	var retentionData []models.UserRetention
	_, err = adminO.QueryTable("member_activity_log").
		Filter("statistic_date__gte", startDate).
		Filter("statistic_date__lte", endDate).
		OrderBy("statistic_date").
		All(&retentionData)

	if err != nil {
		logger.Error("Failed to get user retention", zap.Error(err))
		c.Data["json"] = utils.DatabaseErrorResponse(err)
		c.ServeJSON()
		return
	}

	c.Data["json"] = utils.SuccessResponse(retentionData)
	c.ServeJSON()
}

// DailyRevenue 获取日收入统计
// @Title 获取日收入统计
// @Description 获取指定时间范围内的日收入统计数据
// @Param start_date query string false "开始日期 YYYY-MM-DD"
// @Param end_date query string false "结束日期 YYYY-MM-DD"
// @Param days query int false "统计天数" default(30)
// @Success 200 {object} utils.Response{data=models.RevenueChart}
// @Security ApiKeyAuth
// @router /revenue/daily [get]
func (c *StatisticsController) DailyRevenue() {
	var req DailyUsersRequest
	if err := c.ParseForm(&req); err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid request parameters")
		c.ServeJSON()
		return
	}

	// 设置默认值
	if req.Days <= 0 {
		req.Days = 30
	}

	var startTime, endTime time.Time
	var err error

	if req.StartDate != "" && req.EndDate != "" {
		startTime, err = time.Parse("2006-01-02", req.StartDate)
		if err != nil {
			c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid start_date format")
			c.ServeJSON()
			return
		}
		endTime, err = time.Parse("2006-01-02", req.EndDate)
		if err != nil {
			c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid end_date format")
			c.ServeJSON()
			return
		}
	} else {
		endTime = time.Now().Truncate(24 * time.Hour)
		startTime = endTime.Add(-time.Duration(req.Days) * 24 * time.Hour)
	}

	adminO := database.GetAdminOrm()

	// 获取收入数据
	var incomeData []models.IncomeDay
	_, err = adminO.QueryTable("income_day").
		Filter("income_date__gte", startTime.Unix()).
		Filter("income_date__lte", endTime.Unix()).
		OrderBy("income_date").
		All(&incomeData)

	if err != nil {
		logger.Error("Failed to get daily revenue", zap.Error(err))
		c.Data["json"] = utils.DatabaseErrorResponse(err)
		c.ServeJSON()
		return
	}

	// 构建图表数据
	var chart models.RevenueChart
	for _, data := range incomeData {
		date := time.Unix(int64(data.IncomeDate), 0).Format("2006-01-02")
		chart.Dates = append(chart.Dates, date)
		chart.Revenue = append(chart.Revenue, data.FinalIncome)
		// TODO: 添加付费人数统计
		chart.PayCount = append(chart.PayCount, 0)
	}

	c.Data["json"] = utils.SuccessResponse(chart)
	c.ServeJSON()
}

// MonthlyRevenue 获取月收入统计
// @Title 获取月收入统计
// @Description 获取月度收入统计数据
// @Param months query int false "统计月数" default(12)
// @Success 200 {object} utils.Response{data=models.RevenueChart}
// @Security ApiKeyAuth
// @router /revenue/monthly [get]
func (c *StatisticsController) MonthlyRevenue() {
	monthsStr := c.GetString("months", "12")
	months, err := strconv.Atoi(monthsStr)
	if err != nil || months <= 0 {
		months = 12
	}

	adminO := database.GetAdminOrm()

	// 构建月度收入查询
	var chart models.RevenueChart
	now := time.Now()

	for i := months - 1; i >= 0; i-- {
		monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location()).AddDate(0, -i, 0)
		monthEnd := monthStart.AddDate(0, 1, 0)

		// 查询该月收入
		var monthlyIncome []orm.Params
		sql := "SELECT SUM(final_income) as total FROM income_day WHERE income_date >= ? AND income_date < ?"
		_, err := adminO.Raw(sql, monthStart.Unix(), monthEnd.Unix()).Values(&monthlyIncome)

		monthStr := monthStart.Format("2006-01")
		chart.Dates = append(chart.Dates, monthStr)

		if err != nil {
			logger.Error("Failed to get monthly revenue", zap.Error(err), zap.String("month", monthStr))
			chart.Revenue = append(chart.Revenue, 0)
		} else if len(monthlyIncome) > 0 {
			if total, ok := monthlyIncome[0]["total"].(string); ok {
				if totalFloat, err := strconv.ParseFloat(total, 64); err == nil {
					chart.Revenue = append(chart.Revenue, totalFloat)
				} else {
					chart.Revenue = append(chart.Revenue, 0)
				}
			} else {
				chart.Revenue = append(chart.Revenue, 0)
			}
		} else {
			chart.Revenue = append(chart.Revenue, 0)
		}

		// TODO: 添加月付费人数统计
		chart.PayCount = append(chart.PayCount, 0)
	}

	c.Data["json"] = utils.SuccessResponse(chart)
	c.ServeJSON()
}

// GameSummary 获取游戏数据汇总
// @Title 获取游戏数据汇总
// @Description 获取所有游戏的汇总统计数据
// @Param date query string false "统计日期 YYYY-MM-DD" default(today)
// @Success 200 {object} utils.Response{data=[]models.GameStats}
// @Security ApiKeyAuth
// @router /games/summary [get]
func (c *StatisticsController) GameSummary() {
	dateStr := c.GetString("date", time.Now().Format("2006-01-02"))

	// 验证日期格式
	_, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid date format")
		c.ServeJSON()
		return
	}

	adminO := database.GetAdminOrm()

	// 获取游戏统计数据
	var gameStats []models.GameStats
	_, err = adminO.QueryTable("game_stats").
		Filter("date", dateStr).
		OrderBy("game_type").
		All(&gameStats)

	if err != nil {
		logger.Error("Failed to get game summary", zap.Error(err))
		c.Data["json"] = utils.DatabaseErrorResponse(err)
		c.ServeJSON()
		return
	}

	c.Data["json"] = utils.SuccessResponse(gameStats)
	c.ServeJSON()
}

// GameStats 获取指定游戏统计
// @Title 获取指定游戏统计
// @Description 获取指定游戏类型的详细统计数据
// @Param game_type path string true "游戏类型"
// @Param days query int false "统计天数" default(7)
// @Success 200 {object} utils.Response{data=[]models.GameStats}
// @Security ApiKeyAuth
// @router /games/:game_type [get]
func (c *StatisticsController) GameStats() {
	gameType := c.Ctx.Input.Param(":game_type")
	if gameType == "" {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Game type is required")
		c.ServeJSON()
		return
	}

	daysStr := c.GetString("days", "7")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days <= 0 {
		days = 7
	}

	// 计算时间范围
	endDate := time.Now().Format("2006-01-02")
	startDate := time.Now().AddDate(0, 0, -days).Format("2006-01-02")

	adminO := database.GetAdminOrm()

	// 获取指定游戏的统计数据
	var gameStats []models.GameStats
	_, err = adminO.QueryTable("game_stats").
		Filter("game_type", gameType).
		Filter("date__gte", startDate).
		Filter("date__lte", endDate).
		OrderBy("date").
		All(&gameStats)

	if err != nil {
		logger.Error("Failed to get game stats", zap.Error(err), zap.String("game_type", gameType))
		c.Data["json"] = utils.DatabaseErrorResponse(err)
		c.ServeJSON()
		return
	}

	c.Data["json"] = utils.SuccessResponse(gameStats)
	c.ServeJSON()
}
