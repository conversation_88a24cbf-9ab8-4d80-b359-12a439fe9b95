package config

import (
	"fmt"
	"strings"

	"github.com/spf13/viper"
)

var cfg *viper.Viper

// Init 初始化配置
func Init() error {
	cfg = viper.New()
	
	// 设置配置文件名和路径
	cfg.SetConfigName("app")
	cfg.SetConfigType("yaml")
	cfg.AddConfigPath("./conf")
	cfg.AddConfigPath("../conf")
	cfg.AddConfigPath("../../conf")
	
	// 设置环境变量前缀
	cfg.SetEnvPrefix("KAKABS")
	cfg.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	cfg.AutomaticEnv()
	
	// 读取配置文件
	if err := cfg.ReadInConfig(); err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}
	
	return nil
}

// GetString 获取字符串配置
func GetString(key string) string {
	return cfg.GetString(key)
}

// GetInt 获取整数配置
func GetInt(key string) int {
	return cfg.GetInt(key)
}

// GetBool 获取布尔配置
func GetBool(key string) bool {
	return cfg.GetBool(key)
}

// GetStringSlice 获取字符串切片配置
func GetStringSlice(key string) []string {
	return cfg.GetStringSlice(key)
}

// GetStringMap 获取字符串映射配置
func GetStringMap(key string) map[string]interface{} {
	return cfg.GetStringMap(key)
}

// GetViper 获取viper实例
func GetViper() *viper.Viper {
	return cfg
}

// DatabaseConfig 数据库配置结构
type DatabaseConfig struct {
	Driver          string `mapstructure:"driver"`
	Host            string `mapstructure:"host"`
	Port            int    `mapstructure:"port"`
	Username        string `mapstructure:"username"`
	Password        string `mapstructure:"password"`
	Database        string `mapstructure:"database"`
	Charset         string `mapstructure:"charset"`
	MaxIdleConns    int    `mapstructure:"max_idle_conns"`
	MaxOpenConns    int    `mapstructure:"max_open_conns"`
	ConnMaxLifetime int    `mapstructure:"conn_max_lifetime"`
}

// GetDatabaseConfig 获取数据库配置
func GetDatabaseConfig(name string) (*DatabaseConfig, error) {
	var config DatabaseConfig
	key := fmt.Sprintf("database.%s", name)
	
	if err := cfg.UnmarshalKey(key, &config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal database config %s: %w", name, err)
	}
	
	return &config, nil
}

// RedisConfig Redis配置结构
type RedisConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
	PoolSize int    `mapstructure:"pool_size"`
}

// GetRedisConfig 获取Redis配置
func GetRedisConfig() (*RedisConfig, error) {
	var config RedisConfig
	
	if err := cfg.UnmarshalKey("redis", &config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal redis config: %w", err)
	}
	
	return &config, nil
}

// JWTConfig JWT配置结构
type JWTConfig struct {
	Secret      string `mapstructure:"secret"`
	ExpireHours int    `mapstructure:"expire_hours"`
	Issuer      string `mapstructure:"issuer"`
}

// GetJWTConfig 获取JWT配置
func GetJWTConfig() (*JWTConfig, error) {
	var config JWTConfig
	
	if err := cfg.UnmarshalKey("jwt", &config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal jwt config: %w", err)
	}
	
	return &config, nil
}
