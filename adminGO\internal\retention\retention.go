package retention

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"e:\study\admin.kakabs.com\adminGO\internal\config"
)

// 留存率计算服务
type RetentionService struct {
	DB *gorm.DB
}

// 初始化数据库连接
func InitDB() (*gorm.DB, error) {
	// 加载配置
	cfg := config.LoadConfig()

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		cfg.DBUser, cfg.DBPassword, cfg.DBHost, cfg.DBPort, cfg.DBName)
	return gorm.Open(mysql.Open(dsn), &gorm.Config{})
}

// 获取用户ID交集，计算留存率
func getRetentionRate(prevUsers, currUsers []string) float64 {
	if len(prevUsers) == 0 {
		return 0
	}

	// 使用map提高查找效率
	userMap := make(map[string]bool)
	for _, userID := range prevUsers {
		userMap[userID] = true
	}

	// 计算交集数量
	intersectionCount := 0
	for _, userID := range currUsers {
		if userMap[userID] {
			intersectionCount++
		}
	}

	// 计算留存率
	return float64(intersectionCount) / float64(len(prevUsers)) * 100
}

// 获取某日的新增用户
func (s *RetentionService) getNewUsers(date string) ([]string, int, error) {
	var users []struct {
		UserID string `gorm:"column:userid"`
	}

	// 解析日期
	t, err := time.Parse("2006-01-02", date)
	if err != nil {
		return nil, 0, fmt.Errorf("invalid date format: %v", err)
	}

	// 计算当天的开始和结束时间戳
	startTime := t.Unix()
	endTime := t.Add(24*time.Hour).Unix() - 1

	// 查询当天注册的用户
	err = s.DB.Table("user").
		Where("reg_time >= ? AND reg_time < ?", startTime, endTime).
		Find(&users).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to get new users: %v", err)
	}

	// 提取用户ID
	userIDs := make([]string, len(users))
	for i, user := range users {
		userIDs[i] = user.UserID
	}

	return userIDs, len(userIDs), nil
}

// 获取某日的活跃用户，可以按游戏ID过滤
func (s *RetentionService) getActiveUsers(date string, gameID string) ([]string, int, error) {
	var users []struct {
		UserID string `gorm:"column:userid"`
	}

	// 解析日期
	t, err := time.Parse("2006-01-02", date)
	if err != nil {
		return nil, 0, fmt.Errorf("invalid date format: %v", err)
	}

	// 计算当天的开始和结束时间戳
	startTime := t.Unix()
	endTime := t.Add(24*time.Hour).Unix() - 1

	// 构建查询
	query := s.DB.Table("user_last_game").
		Distinct("userid").
		Where("play_date >= ? AND play_date < ?", startTime, endTime)

	// 如果指定了游戏ID，则添加过滤条件
	if gameID != "" {
		gameIDInt, err := strconv.Atoi(gameID)
		if err != nil {
			return nil, 0, fmt.Errorf("invalid game ID: %v", err)
		}
		query = query.Where("gameid = ?", gameIDInt)
	}

	// 执行查询
	err = query.Find(&users).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to get active users: %v", err)
	}

	// 提取用户ID
	userIDs := make([]string, len(users))
	for i, user := range users {
		userIDs[i] = user.UserID
	}

	return userIDs, len(userIDs), nil
}

// GetNDayRetention 计算N日留存率的通用函数
func GetNDayRetention(c *gin.Context, n int) {
	// 参数校验
	if n <= 0 || n > 365 {
		// 如果n不是正整数或超过最大限制(365天)，默认使用1天(次日留存)
		n = 1
		c.JSON(http.StatusBadRequest, gin.H{
			"error":  "Invalid retention days parameter",
			"message": "Retention days must be between 1 and 365. Using default value: 1 day",
		})
		return
	}

	// 获取日期参数
	dateStr := c.Query("date")
	if dateStr == "" {
		// 默认使用n天前的日期
	defaultDate := time.Now().AddDate(0, 0, -n)
	dateStr = defaultDate.Format("2006-01-02")
	}

	// 获取游戏ID参数
	gameID := c.Query("game_id")

	// 初始化数据库
	db, err := InitDB()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to initialize database: %v", err)})
		return
	}

	service := &RetentionService{DB: db}

	// 获取指定日期的新增用户
	newUsers, newUserCount, err := service.getNewUsers(dateStr)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 解析日期，计算n天后
	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid date format"})
		return
	}
	nDaysLater := date.AddDate(0, 0, n).Format("2006-01-02")

	// 获取n天后的活跃用户
	activeUsers, activeUserCount, err := service.getActiveUsers(nDaysLater, gameID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 计算n日留存率
	retention := getRetentionRate(newUsers, activeUsers)

	// 返回结果
	result := gin.H{
		"date":              dateStr,
		"new_user_count":    newUserCount,
		"active_user_count": activeUserCount,
		fmt.Sprintf("%d_days_retention", n): retention,
		"retention_rate":    fmt.Sprintf("%.2f%%", retention),
	}

	// 如果指定了游戏ID，添加到结果中
	if gameID != "" {
		result["game_id"] = gameID
	}

	c.JSON(http.StatusOK, result)
}

// GetDailyRetention 计算次日留存率
func GetDailyRetention(c *gin.Context) {
	GetNDayRetention(c, 1)
}

// Get3DaysRetention 计算3日留存率
func Get3DaysRetention(c *gin.Context) {
	GetNDayRetention(c, 3)
}

// Get7DaysRetention 计算7日留存率
func Get7DaysRetention(c *gin.Context) {
	GetNDayRetention(c, 7)
}

// Get15DaysRetention 计算15日留存率
func Get15DaysRetention(c *gin.Context) {
	GetNDayRetention(c, 15)
}

// Get30DaysRetention 计算30日留存率
func Get30DaysRetention(c *gin.Context) {
	GetNDayRetention(c, 30)
}

// GetRetentionUsers 获取n日留存用户列表
func (s *RetentionService) GetRetentionUsers(date string, n int, gameID string) ([]string, error) {
	// 获取指定日期的新增用户
	newUsers, _, err := s.getNewUsers(date)
	if err != nil {
		return nil, fmt.Errorf("failed to get new users: %v", err)
	}

	// 解析日期，计算n天后
	dateObj, err := time.Parse("2006-01-02", date)
	if err != nil {
		return nil, fmt.Errorf("invalid date format: %v", err)
	}
	nDaysLater := dateObj.AddDate(0, 0, n).Format("2006-01-02")

	// 获取n天后的活跃用户
	activeUsers, _, err := s.getActiveUsers(nDaysLater, gameID)
	if err != nil {
		return nil, fmt.Errorf("failed to get active users: %v", err)
	}

	// 计算交集（留存用户）
	userMap := make(map[string]bool)
	for _, userID := range newUsers {
		userMap[userID] = true
	}

	var retentionUsers []string
	for _, userID := range activeUsers {
		if userMap[userID] {
			retentionUsers = append(retentionUsers, userID)
		}
	}

	return retentionUsers, nil
}

// GetRetentionUsersHandler 处理获取留存用户列表的HTTP请求
func GetRetentionUsersHandler(c *gin.Context) {
	// 获取日期参数
	dateStr := c.Query("date")
	if dateStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Date parameter is required"})
		return
	}

	// 获取天数参数
	daysStr := c.Query("days")
	days := 1 // 默认次日留存
	if daysStr != "" {
		var err error
		days, err = strconv.Atoi(daysStr)
		if err != nil || days <= 0 || days > 365 {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Invalid days parameter",
				"message": "Days must be between 1 and 365",
			})
			return
		}
	}

	// 获取游戏ID参数
	gameID := c.Query("game_id")

	// 初始化数据库
	db, err := InitDB()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to initialize database: %v", err)})
		return
	}

	service := &RetentionService{DB: db}

	// 获取留存用户列表
	retentionUsers, err := service.GetRetentionUsers(dateStr, days, gameID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 返回结果
	result := gin.H{
		"date":             dateStr,
		"retention_days":   days,
		"retention_count":  len(retentionUsers),
		"retention_users":  retentionUsers,
	}

	// 如果指定了游戏ID，添加到结果中
	if gameID != "" {
		result["game_id"] = gameID
	}

	c.JSON(http.StatusOK, result)
}