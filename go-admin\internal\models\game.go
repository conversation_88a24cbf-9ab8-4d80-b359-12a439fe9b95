package models

import (
	"time"

	"github.com/beego/beego/v2/client/orm"
)

// GameConfig 游戏配置模型
type GameConfig struct {
	ID                        int       `orm:"auto;pk;column(id)" json:"id"`
	GameType                  string    `orm:"size(50);column(game_type)" json:"game_type"`                   // 游戏类型
	RobotEnterTime            int       `orm:"column(robot_enter_time);default(3000)" json:"robot_enter_time"` // 机器人进入时间(毫秒)
	DarkPoolThreshold         int       `orm:"column(dark_pool_threshold);default(0)" json:"dark_pool_threshold"` // 暗池阈值
	ZuopaiRate                int       `orm:"column(zuopai_rate);default(5000)" json:"zuopai_rate"`         // 作牌率
	MinControlValue           int       `orm:"column(min_control_value);default(0)" json:"min_control_value"` // 最小控制值
	MaxControlValue           int       `orm:"column(max_control_value);default(0)" json:"max_control_value"` // 最大控制值
	ControlRate               int       `orm:"column(control_rate);default(0)" json:"control_rate"`          // 控制比率
	PlayerWinRate             int       `orm:"column(player_win_rate);default(0)" json:"player_win_rate"`    // 玩家胜率
	AIWinRate                 int       `orm:"column(ai_win_rate);default(0)" json:"ai_win_rate"`            // AI胜率
	RobotGuestRate            int       `orm:"column(robot_guest_rate);default(0)" json:"robot_guest_rate"`  // 机器人猜牌概率
	PlayerWinnerPreTingRate   int       `orm:"column(player_winner_pre_ting_rate);default(0)" json:"player_winner_pre_ting_rate"`   // 判赢玩家听牌前喂牌概率
	PlayerWinnerAfterTingRate int       `orm:"column(player_winner_after_ting_rate);default(0)" json:"player_winner_after_ting_rate"` // 判赢玩家听牌后胡牌概率
	PlayerLoserPreTingRate    int       `orm:"column(player_loser_pre_ting_rate);default(0)" json:"player_loser_pre_ting_rate"`    // 判输玩家听牌前喂牌概率
	PlayerLoserAfterTingRate  int       `orm:"column(player_loser_after_ting_rate);default(0)" json:"player_loser_after_ting_rate"` // 判输玩家听牌后不能胡牌概率
	RobotWinnerPreTingRate    int       `orm:"column(robot_winner_pre_ting_rate);default(0)" json:"robot_winner_pre_ting_rate"`    // 判赢机器人听牌前喂牌概率
	Status                    int       `orm:"column(status);default(1)" json:"status"`                     // 状态 0禁用 1启用
	CreateTime                time.Time `orm:"auto_now_add;type(datetime);column(create_time)" json:"create_time"`
	UpdateTime                time.Time `orm:"auto_now;type(datetime);column(update_time)" json:"update_time"`
}

// TableName 设置表名
func (g *GameConfig) TableName() string {
	return "game_configs"
}

// GameList 游戏列表模型
type GameList struct {
	ID               int       `orm:"auto;pk;column(id)" json:"id"`
	GameID           int       `orm:"column(game_id);unique" json:"game_id"`                // 游戏ID
	GameName         string    `orm:"size(100);column(game_name)" json:"game_name"`         // 游戏名称
	GameType         string    `orm:"size(50);column(game_type)" json:"game_type"`          // 游戏类型
	GameDesc         string    `orm:"size(500);column(game_desc)" json:"game_desc"`         // 游戏描述
	DefaultFkSetting string    `orm:"type(text);column(default_fk_setting)" json:"default_fk_setting"` // 默认福卡设置
	ShowHall         int       `orm:"column(show_hall);default(1)" json:"show_hall"`        // 大厅显示 0不显示 1显示
	ShowHouse        int       `orm:"column(show_house);default(1)" json:"show_house"`      // 茶馆显示 0不显示 1显示
	Sort             int       `orm:"column(sort);default(0)" json:"sort"`                  // 排序
	Status           int       `orm:"column(status);default(1)" json:"status"`              // 状态 0禁用 1启用
	CreateTime       time.Time `orm:"auto_now_add;type(datetime);column(create_time)" json:"create_time"`
	UpdateTime       time.Time `orm:"auto_now;type(datetime);column(update_time)" json:"update_time"`
}

// TableName 设置表名
func (g *GameList) TableName() string {
	return "game_list"
}

// Match 比赛模型
type Match struct {
	ID           int       `orm:"auto;pk;column(id)" json:"id"`
	MatchID      string    `orm:"size(64);unique;column(match_id)" json:"match_id"`      // 比赛ID
	MatchName    string    `orm:"size(255);column(match_name)" json:"match_name"`        // 比赛名称
	GameType     string    `orm:"size(50);column(game_type)" json:"game_type"`           // 游戏类型
	GameName     string    `orm:"size(100);column(game_name)" json:"game_name"`          // 游戏名称
	MatchType    int       `orm:"column(match_type)" json:"match_type"`                  // 比赛类型 1定时赛 2满人赛
	PlayerMin    int       `orm:"column(player_min)" json:"player_min"`                  // 最少人数
	PlayerMax    int       `orm:"column(player_max)" json:"player_max"`                  // 最多人数
	CostVas      int       `orm:"column(cost_vas)" json:"cost_vas"`                      // 报名费用(钻石)
	AwardDes     string    `orm:"size(500);column(award_des)" json:"award_des"`          // 奖励描述
	StartTime    time.Time `orm:"type(datetime);column(start_time)" json:"start_time"`   // 开始时间
	EndTime      time.Time `orm:"type(datetime);column(end_time)" json:"end_time"`       // 结束时间
	OverState    int       `orm:"column(over_state);default(0)" json:"over_state"`       // 比赛状态 0未开始 1进行中 2已结束
	OpenType     int       `orm:"column(open_type);default(1)" json:"open_type"`         // 开赛类型 1满人开赛 2定时开赛
	IsSelf       int       `orm:"column(is_self);default(0)" json:"is_self"`             // 是否自建赛事
	XqqID        int       `orm:"column(xqq_id);default(0)" json:"xqq_id"`               // 群组ID
	CreateTime   time.Time `orm:"auto_now_add;type(datetime);column(create_time)" json:"create_time"`
	UpdateTime   time.Time `orm:"auto_now;type(datetime);column(update_time)" json:"update_time"`
}

// TableName 设置表名
func (m *Match) TableName() string {
	return "matches"
}

// MatchPlayer 比赛参与者模型
type MatchPlayer struct {
	ID         int       `orm:"auto;pk;column(id)" json:"id"`
	MatchID    string    `orm:"size(64);column(match_id)" json:"match_id"`             // 比赛ID
	UserID     int       `orm:"column(user_id)" json:"user_id"`                        // 用户ID
	Nickname   string    `orm:"size(100);column(nickname)" json:"nickname"`           // 用户昵称
	Avatar     string    `orm:"size(255);column(avatar)" json:"avatar"`               // 用户头像
	JoinTime   time.Time `orm:"auto_now_add;type(datetime);column(join_time)" json:"join_time"` // 参与时间
	Rank       int       `orm:"column(rank);default(0)" json:"rank"`                  // 排名
	Score      int       `orm:"column(score);default(0)" json:"score"`                // 得分
	Award      string    `orm:"size(255);column(award)" json:"award"`                 // 获得奖励
	Status     int       `orm:"column(status);default(1)" json:"status"`              // 状态 1正常 0淘汰
}

// TableName 设置表名
func (m *MatchPlayer) TableName() string {
	return "match_players"
}

// MatchControl 比赛控制模型
type MatchControl struct {
	ID              int       `orm:"auto;pk;column(id)" json:"id"`
	TypeID          int       `orm:"column(type_id)" json:"type_id"`                        // 比赛类型ID
	NeedControl     int       `orm:"column(need_control);default(0)" json:"need_control"`   // 是否需要控制
	ControlRank     int       `orm:"column(control_rank);default(0)" json:"control_rank"`   // 控制排名
	ControlTurn     int       `orm:"column(control_turn);default(0)" json:"control_turn"`   // 控制轮数
	FirstTurnKiller int       `orm:"column(first_turn_killer);default(0)" json:"first_turn_killer"` // 首轮杀手
	CreateTime      time.Time `orm:"auto_now_add;type(datetime);column(create_time)" json:"create_time"`
	UpdateTime      time.Time `orm:"auto_now;type(datetime);column(update_time)" json:"update_time"`
}

// TableName 设置表名
func (m *MatchControl) TableName() string {
	return "match_controls"
}

// Robot 机器人模型
type Robot struct {
	ID         int       `orm:"auto;pk;column(id)" json:"id"`
	UserID     int       `orm:"column(user_id);unique" json:"user_id"`             // 机器人用户ID
	Nickname   string    `orm:"size(100);column(nickname)" json:"nickname"`       // 机器人昵称
	Avatar     string    `orm:"size(255);column(avatar)" json:"avatar"`           // 机器人头像
	GameTypes  string    `orm:"size(255);column(game_types)" json:"game_types"`   // 支持的游戏类型(逗号分隔)
	WinRate    int       `orm:"column(win_rate);default(5000)" json:"win_rate"`   // 胜率(万分比)
	Level      int       `orm:"column(level);default(1)" json:"level"`            // 机器人等级
	Status     int       `orm:"column(status);default(1)" json:"status"`          // 状态 0禁用 1启用
	CreateTime time.Time `orm:"auto_now_add;type(datetime);column(create_time)" json:"create_time"`
	UpdateTime time.Time `orm:"auto_now;type(datetime);column(update_time)" json:"update_time"`
}

// TableName 设置表名
func (r *Robot) TableName() string {
	return "robots"
}

// HouseGame 茶馆游戏模型
type HouseGame struct {
	ID        int       `orm:"auto;pk;column(id)" json:"id"`
	HouseID   int       `orm:"column(house_id)" json:"house_id"`                  // 茶馆ID
	GameID    int       `orm:"column(game_id)" json:"game_id"`                    // 游戏ID
	Setting   string    `orm:"type(text);column(setting)" json:"setting"`        // 游戏设置
	Open      int       `orm:"column(open);default(1)" json:"open"`               // 是否开启 0关闭 1开启
	TableDes  string    `orm:"size(500);column(table_des)" json:"table_des"`     // 桌子描述
	CreateTime time.Time `orm:"auto_now_add;type(datetime);column(create_time)" json:"create_time"`
	UpdateTime time.Time `orm:"auto_now;type(datetime);column(update_time)" json:"update_time"`
}

// TableName 设置表名
func (h *HouseGame) TableName() string {
	return "house_games"
}

// GameRoom 游戏房间模型
type GameRoom struct {
	ID         int       `orm:"auto;pk;column(id)" json:"id"`
	RoomID     string    `orm:"size(64);unique;column(room_id)" json:"room_id"`    // 房间ID
	GameType   string    `orm:"size(50);column(game_type)" json:"game_type"`       // 游戏类型
	RoomType   int       `orm:"column(room_type)" json:"room_type"`                // 房间类型 1金币场 2比赛场 3茶馆
	PlayerCount int      `orm:"column(player_count);default(0)" json:"player_count"` // 当前人数
	MaxPlayers int       `orm:"column(max_players)" json:"max_players"`            // 最大人数
	BaseScore  int       `orm:"column(base_score)" json:"base_score"`              // 底分
	Status     int       `orm:"column(status);default(0)" json:"status"`          // 状态 0等待 1游戏中 2已结束
	CreateTime time.Time `orm:"auto_now_add;type(datetime);column(create_time)" json:"create_time"`
	UpdateTime time.Time `orm:"auto_now;type(datetime);column(update_time)" json:"update_time"`
}

// TableName 设置表名
func (g *GameRoom) TableName() string {
	return "game_rooms"
}

// GameRecord 游戏记录模型
type GameRecord struct {
	ID         int64     `orm:"auto;pk;column(id)" json:"id"`
	RoomID     string    `orm:"size(64);column(room_id)" json:"room_id"`           // 房间ID
	GameType   string    `orm:"size(50);column(game_type)" json:"game_type"`       // 游戏类型
	Players    string    `orm:"type(text);column(players)" json:"players"`        // 玩家信息(JSON)
	GameData   string    `orm:"type(longtext);column(game_data)" json:"game_data"` // 游戏数据(JSON)
	StartTime  time.Time `orm:"type(datetime);column(start_time)" json:"start_time"` // 开始时间
	EndTime    time.Time `orm:"type(datetime);column(end_time)" json:"end_time"`   // 结束时间
	Duration   int       `orm:"column(duration)" json:"duration"`                  // 游戏时长(秒)
	CreateTime time.Time `orm:"auto_now_add;type(datetime);column(create_time)" json:"create_time"`
}

// TableName 设置表名
func (g *GameRecord) TableName() string {
	return "game_records"
}

// 请求和响应结构体

// GameConfigRequest 游戏配置请求
type GameConfigRequest struct {
	GameType                  string `json:"game_type" valid:"Required"`
	RobotEnterTime            int    `json:"robot_enter_time"`
	DarkPoolThreshold         int    `json:"dark_pool_threshold"`
	ZuopaiRate                int    `json:"zuopai_rate"`
	MinControlValue           int    `json:"min_control_value"`
	MaxControlValue           int    `json:"max_control_value"`
	ControlRate               int    `json:"control_rate"`
	PlayerWinRate             int    `json:"player_win_rate"`
	AIWinRate                 int    `json:"ai_win_rate"`
	RobotGuestRate            int    `json:"robot_guest_rate"`
	PlayerWinnerPreTingRate   int    `json:"player_winner_pre_ting_rate"`
	PlayerWinnerAfterTingRate int    `json:"player_winner_after_ting_rate"`
	PlayerLoserPreTingRate    int    `json:"player_loser_pre_ting_rate"`
	PlayerLoserAfterTingRate  int    `json:"player_loser_after_ting_rate"`
	RobotWinnerPreTingRate    int    `json:"robot_winner_pre_ting_rate"`
}

// MatchRequest 比赛请求
type MatchRequest struct {
	MatchName string    `json:"match_name" valid:"Required"`
	GameType  string    `json:"game_type" valid:"Required"`
	GameName  string    `json:"game_name" valid:"Required"`
	MatchType int       `json:"match_type" valid:"Required"`
	PlayerMin int       `json:"player_min" valid:"Required"`
	PlayerMax int       `json:"player_max" valid:"Required"`
	CostVas   int       `json:"cost_vas"`
	AwardDes  string    `json:"award_des"`
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	OpenType  int       `json:"open_type"`
}

// RobotRequest 机器人请求
type RobotRequest struct {
	UserID    int    `json:"user_id" valid:"Required"`
	Nickname  string `json:"nickname" valid:"Required"`
	Avatar    string `json:"avatar"`
	GameTypes string `json:"game_types" valid:"Required"`
	WinRate   int    `json:"win_rate"`
	Level     int    `json:"level"`
}

// MatchQuery 比赛查询请求
type MatchQuery struct {
	Page      int    `form:"page"`
	Size      int    `form:"size"`
	MatchName string `form:"match_name"`
	GameType  string `form:"game_type"`
	OverState int    `form:"over_state"`
	StartDate string `form:"start_date"`
	EndDate   string `form:"end_date"`
}

func init() {
	// 注册游戏相关模型
	orm.RegisterModelWithPrefix("", new(GameConfig))
	orm.RegisterModelWithPrefix("", new(GameList))
	orm.RegisterModelWithPrefix("", new(Match))
	orm.RegisterModelWithPrefix("", new(MatchPlayer))
	orm.RegisterModelWithPrefix("", new(MatchControl))
	orm.RegisterModelWithPrefix("", new(Robot))
	orm.RegisterModelWithPrefix("", new(HouseGame))
	orm.RegisterModelWithPrefix("", new(GameRoom))
	orm.RegisterModelWithPrefix("", new(GameRecord))
}
