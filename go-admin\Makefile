# Makefile for Kakabs Admin Go Project

# 变量定义
APP_NAME = kakabs-admin
VERSION = 1.0.0
BUILD_TIME = $(shell date +%Y-%m-%d_%H:%M:%S)
GIT_COMMIT = $(shell git rev-parse --short HEAD)
GO_VERSION = $(shell go version | awk '{print $$3}')

# 构建标志
LDFLAGS = -ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GitCommit=$(GIT_COMMIT)"

# 默认目标
.PHONY: all
all: clean deps build

# 安装依赖
.PHONY: deps
deps:
	@echo "Installing dependencies..."
	go mod tidy
	go mod download

# 代码格式化
.PHONY: fmt
fmt:
	@echo "Formatting code..."
	go fmt ./...

# 代码检查
.PHONY: lint
lint:
	@echo "Running linter..."
	golangci-lint run

# 运行测试
.PHONY: test
test:
	@echo "Running tests..."
	go test -v ./...

# 运行测试并生成覆盖率报告
.PHONY: test-coverage
test-coverage:
	@echo "Running tests with coverage..."
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html

# 构建应用
.PHONY: build
build:
	@echo "Building application..."
	go build $(LDFLAGS) -o bin/$(APP_NAME) main.go

# 构建Linux版本
.PHONY: build-linux
build-linux:
	@echo "Building for Linux..."
	GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o bin/$(APP_NAME)-linux main.go

# 构建Windows版本
.PHONY: build-windows
build-windows:
	@echo "Building for Windows..."
	GOOS=windows GOARCH=amd64 go build $(LDFLAGS) -o bin/$(APP_NAME)-windows.exe main.go

# 构建所有平台版本
.PHONY: build-all
build-all: build-linux build-windows build

# 运行应用（开发模式）
.PHONY: run
run:
	@echo "Running application in development mode..."
	go run main.go

# 运行应用（生产模式）
.PHONY: run-prod
run-prod: build
	@echo "Running application in production mode..."
	./bin/$(APP_NAME)

# 生成API文档
.PHONY: docs
docs:
	@echo "Generating API documentation..."
	swag init -g main.go -o docs/

# 清理构建文件
.PHONY: clean
clean:
	@echo "Cleaning build files..."
	rm -rf bin/
	rm -rf docs/
	rm -f coverage.out coverage.html

# 创建目录结构
.PHONY: init-dirs
init-dirs:
	@echo "Creating directory structure..."
	mkdir -p bin logs uploads docs

# Docker构建
.PHONY: docker-build
docker-build:
	@echo "Building Docker image..."
	docker build -t $(APP_NAME):$(VERSION) .
	docker tag $(APP_NAME):$(VERSION) $(APP_NAME):latest

# Docker运行
.PHONY: docker-run
docker-run:
	@echo "Running Docker container..."
	docker run -d \
		--name $(APP_NAME) \
		-p 8080:8080 \
		-v $(PWD)/conf:/app/conf \
		-v $(PWD)/logs:/app/logs \
		$(APP_NAME):latest

# Docker停止
.PHONY: docker-stop
docker-stop:
	@echo "Stopping Docker container..."
	docker stop $(APP_NAME) || true
	docker rm $(APP_NAME) || true

# 安装开发工具
.PHONY: install-tools
install-tools:
	@echo "Installing development tools..."
	go install github.com/swaggo/swag/cmd/swag@latest
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# 数据库迁移
.PHONY: migrate
migrate:
	@echo "Running database migrations..."
	# TODO: 添加数据库迁移命令

# 启动开发环境
.PHONY: dev
dev: deps fmt docs run

# 生产环境部署
.PHONY: deploy
deploy: clean deps test build-linux
	@echo "Deploying to production..."
	# TODO: 添加部署脚本

# 健康检查
.PHONY: health
health:
	@echo "Checking application health..."
	curl -f http://localhost:8080/health || exit 1

# 显示版本信息
.PHONY: version
version:
	@echo "App Name: $(APP_NAME)"
	@echo "Version: $(VERSION)"
	@echo "Build Time: $(BUILD_TIME)"
	@echo "Git Commit: $(GIT_COMMIT)"
	@echo "Go Version: $(GO_VERSION)"

# 显示帮助信息
.PHONY: help
help:
	@echo "Available commands:"
	@echo "  deps          - Install dependencies"
	@echo "  fmt           - Format code"
	@echo "  lint          - Run linter"
	@echo "  test          - Run tests"
	@echo "  test-coverage - Run tests with coverage"
	@echo "  build         - Build application"
	@echo "  build-linux   - Build for Linux"
	@echo "  build-windows - Build for Windows"
	@echo "  build-all     - Build for all platforms"
	@echo "  run           - Run in development mode"
	@echo "  run-prod      - Run in production mode"
	@echo "  docs          - Generate API documentation"
	@echo "  clean         - Clean build files"
	@echo "  docker-build  - Build Docker image"
	@echo "  docker-run    - Run Docker container"
	@echo "  docker-stop   - Stop Docker container"
	@echo "  dev           - Start development environment"
	@echo "  deploy        - Deploy to production"
	@echo "  health        - Check application health"
	@echo "  version       - Show version information"
	@echo "  help          - Show this help message"
