package utils

import (
	"fmt"
	"kakabs-admin/internal/config"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// JWTClaims JWT声明结构
type JWTClaims struct {
	UserID     int    `json:"user_id"`
	AdminName  string `json:"admin_name"`
	AdminType  int    `json:"admin_type"`
	ChannelID  int    `json:"channel_id"`
	jwt.RegisteredClaims
}

// GenerateJWT 生成JWT token
func GenerateJWT(userID int, adminName string, adminType, channelID int) (string, error) {
	jwtConfig, err := config.GetJWTConfig()
	if err != nil {
		return "", fmt.Errorf("failed to get JWT config: %w", err)
	}

	now := time.Now()
	claims := JWTClaims{
		UserID:    userID,
		AdminName: adminName,
		AdminType: adminType,
		ChannelID: channelID,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    jwtConfig.Issuer,
			Subject:   fmt.Sprintf("%d", userID),
			IssuedAt:  jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(now.Add(time.Duration(jwtConfig.ExpireHours) * time.Hour)),
			NotBefore: jwt.NewNumericDate(now),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(jwtConfig.Secret))
	if err != nil {
		return "", fmt.Errorf("failed to sign token: %w", err)
	}

	return tokenString, nil
}

// ParseJWT 解析JWT token
func ParseJWT(tokenString string) (*JWTClaims, error) {
	jwtConfig, err := config.GetJWTConfig()
	if err != nil {
		return nil, fmt.Errorf("failed to get JWT config: %w", err)
	}

	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(jwtConfig.Secret), nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token claims")
}

// RefreshJWT 刷新JWT token
func RefreshJWT(tokenString string) (string, error) {
	claims, err := ParseJWT(tokenString)
	if err != nil {
		return "", fmt.Errorf("failed to parse token: %w", err)
	}

	// 检查token是否即将过期（在过期前1小时内可以刷新）
	if time.Until(claims.ExpiresAt.Time) > time.Hour {
		return "", fmt.Errorf("token is not eligible for refresh")
	}

	// 生成新的token
	return GenerateJWT(claims.UserID, claims.AdminName, claims.AdminType, claims.ChannelID)
}

// ValidateJWT 验证JWT token是否有效
func ValidateJWT(tokenString string) bool {
	_, err := ParseJWT(tokenString)
	return err == nil
}
