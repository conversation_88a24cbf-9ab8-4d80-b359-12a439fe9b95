# 留存率计算服务 (Go实现)

## 项目简介

此项目是基于Go语言的用户留存率计算服务，移植自原PHP项目中的留存率计算逻辑。主要功能包括计算次日留存、3日留存、7日留存、15日留存和30日留存率，并提供RESTful API接口。

## 技术栈

- Go 1.21
- Gin Web框架
- GORM ORM框架
- MySQL数据库

## 项目结构

```
adminGO/
├── main.go             # 项目入口文件
├── go.mod              # 依赖管理文件
├── README.md           # 项目说明文档
└── internal/
    └── retention/
        └── retention.go # 留存率计算核心逻辑
```

## 功能实现

1. **数据库连接**：通过GORM连接MySQL数据库
2. **用户数据获取**：获取指定日期的新增用户和活跃用户
3. **留存率计算**：通过用户ID交集计算留存率
4. **API接口**：提供不同时间周期的留存率查询接口

## API接口

- `GET /api/retention/daily?date=2023-10-01` - 获取指定日期的次日留存率
- `GET /api/retention/3days?date=2023-10-01` - 获取指定日期的3日留存率
- `GET /api/retention/7days?date=2023-10-01` - 获取指定日期的7日留存率
- `GET /api/retention/15days?date=2023-10-01` - 获取指定日期的15日留存率
- `GET /api/retention/30days?date=2023-10-01` - 获取指定日期的30日留存率

> 注：date参数可选，默认使用相应周期前的日期

## 使用方法

1. 安装依赖
```bash
cd e:\study\admin.kakabs.com\adminGO
go mod tidy
```

2. 配置数据库连接
修改 `internal/retention/retention.go` 文件中的数据库连接信息：
```go	dsn := "username:password@tcp(host:port)/database?charset=utf8mb4&parseTime=True&loc=Local"
```

3. 启动服务
```bash
go run main.go
```

4. 访问API
在浏览器或API测试工具中访问：
`http://localhost:8080/api/retention/daily`

## 注意事项

1. 确保Go环境已正确安装
2. 替换数据库连接信息为实际可用的配置
3. 项目依赖需要通过 `go mod tidy` 安装
4. 建议使用环境变量或配置文件管理敏感信息