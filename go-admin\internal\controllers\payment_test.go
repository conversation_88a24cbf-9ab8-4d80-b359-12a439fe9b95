package controllers

import (
	"kakabs-admin/internal/database"
	"kakabs-admin/internal/logger"
	"kakabs-admin/internal/models"
	"kakabs-admin/internal/services"
	"kakabs-admin/internal/utils"
	"net/http"
	"strconv"

	"github.com/beego/beego/v2/server/web"
	"go.uber.org/zap"
)

// PaymentTestController 支付测试控制器
type PaymentTestController struct {
	web.Controller
}

// TestPaymentRequest 支付测试请求
type TestPaymentRequest struct {
	PayType     int     `json:"pay_type"`     // 支付类型 1微信 2支付宝 3苹果 4抖音
	Environment string  `json:"environment"`  // 环境 sandbox/prod
	TestAmount  float64 `json:"test_amount"`  // 测试金额
	UserID      int     `json:"user_id"`      // 测试用户ID
	ProductID   int     `json:"product_id"`   // 测试商品ID
}

// TestAllPayments 测试所有支付方式
// @Title 测试所有支付方式
// @Description 测试所有已配置的支付方式
// @Param environment query string false "测试环境" default(sandbox)
// @Param test_amount query float false "测试金额" default(0.01)
// @Success 200 {object} utils.Response{data=[]services.TestResult}
// @Security ApiKeyAuth
// @router /test/all [get]
func (c *PaymentTestController) TestAllPayments() {
	environment := c.GetString("environment", "sandbox")
	testAmountStr := c.GetString("test_amount", "0.01")
	testAmount, _ := strconv.ParseFloat(testAmountStr, 64)

	// 获取所有启用的支付配置
	o := database.GetOrm()
	var configs []models.PaymentConfig
	_, err := o.QueryTable("payment_configs").Filter("status", 1).All(&configs)
	if err != nil {
		logger.Error("Failed to get payment configs", zap.Error(err))
		c.Data["json"] = utils.ErrorResponse(http.StatusInternalServerError, "Failed to get payment configs")
		c.ServeJSON()
		return
	}

	// 构建测试配置
	var testConfigs []services.TestConfig
	for _, config := range configs {
		testConfigs = append(testConfigs, services.TestConfig{
			PayType:     config.PayType,
			Environment: environment,
			TestAmount:  testAmount,
			UserID:      999999, // 测试用户ID
			ProductID:   1,      // 测试商品ID
		})
	}

	// 创建测试服务
	testService := services.NewPaymentTestService(environment)

	// 执行测试
	results := testService.TestAllPayments(testConfigs)

	logger.Info("Payment test completed",
		zap.String("environment", environment),
		zap.Float64("test_amount", testAmount),
		zap.Int("total_tests", len(results)),
	)

	c.Data["json"] = utils.SuccessResponse(results)
	c.ServeJSON()
}

// TestSinglePayment 测试单个支付方式
// @Title 测试单个支付方式
// @Description 测试指定的支付方式
// @Param body body TestPaymentRequest true "测试请求"
// @Success 200 {object} utils.Response{data=services.TestResult}
// @Security ApiKeyAuth
// @router /test/single [post]
func (c *PaymentTestController) TestSinglePayment() {
	var req TestPaymentRequest
	if err := c.ParseForm(&req); err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid request format")
		c.ServeJSON()
		return
	}

	// 验证参数
	if req.PayType <= 0 || req.PayType > 4 {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid pay type")
		c.ServeJSON()
		return
	}

	if req.Environment == "" {
		req.Environment = "sandbox"
	}

	if req.TestAmount <= 0 {
		req.TestAmount = 0.01
	}

	// 创建测试服务
	testService := services.NewPaymentTestService(req.Environment)

	// 执行测试
	testConfig := services.TestConfig{
		PayType:     req.PayType,
		Environment: req.Environment,
		TestAmount:  req.TestAmount,
		UserID:      req.UserID,
		ProductID:   req.ProductID,
	}

	var result services.TestResult
	switch req.PayType {
	case 1: // 微信支付
		result = testService.TestAllPayments([]services.TestConfig{testConfig})[0]
	case 2: // 支付宝支付
		result = testService.TestAllPayments([]services.TestConfig{testConfig})[0]
	case 3: // 苹果内购
		result = testService.TestAllPayments([]services.TestConfig{testConfig})[0]
	case 4: // 抖音支付
		result = testService.TestAllPayments([]services.TestConfig{testConfig})[0]
	}

	logger.Info("Single payment test completed",
		zap.Int("pay_type", req.PayType),
		zap.String("environment", req.Environment),
		zap.Bool("success", result.Success),
	)

	c.Data["json"] = utils.SuccessResponse(result)
	c.ServeJSON()
}

// TestSmallAmount 小额支付测试
// @Title 小额支付测试
// @Description 在生产环境进行小额支付测试
// @Param pay_type query int true "支付类型"
// @Param amount query float false "测试金额" default(0.01)
// @Success 200 {object} utils.Response{data=services.TestResult}
// @Security ApiKeyAuth
// @router /test/small-amount [get]
func (c *PaymentTestController) TestSmallAmount() {
	payTypeStr := c.GetString("pay_type")
	amountStr := c.GetString("amount", "0.01")

	payType, err := strconv.Atoi(payTypeStr)
	if err != nil || payType <= 0 || payType > 4 {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid pay type")
		c.ServeJSON()
		return
	}

	amount, err := strconv.ParseFloat(amountStr, 64)
	if err != nil || amount <= 0 {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid amount")
		c.ServeJSON()
		return
	}

	// 创建测试服务
	testService := services.NewPaymentTestService("prod")

	// 执行小额支付测试
	result := testService.TestSmallAmountPayment(payType, amount)

	logger.Info("Small amount payment test completed",
		zap.Int("pay_type", payType),
		zap.Float64("amount", amount),
		zap.Bool("success", result.Success),
	)

	c.Data["json"] = utils.SuccessResponse(result)
	c.ServeJSON()
}

// GenerateTestReport 生成测试报告
// @Title 生成测试报告
// @Description 生成支付系统测试报告
// @Param environment query string false "测试环境" default(sandbox)
// @Success 200 {object} utils.Response{data=string}
// @Security ApiKeyAuth
// @router /test/report [get]
func (c *PaymentTestController) GenerateTestReport() {
	environment := c.GetString("environment", "sandbox")

	// 获取所有启用的支付配置
	o := database.GetOrm()
	var configs []models.PaymentConfig
	_, err := o.QueryTable("payment_configs").Filter("status", 1).All(&configs)
	if err != nil {
		logger.Error("Failed to get payment configs", zap.Error(err))
		c.Data["json"] = utils.ErrorResponse(http.StatusInternalServerError, "Failed to get payment configs")
		c.ServeJSON()
		return
	}

	// 构建测试配置
	var testConfigs []services.TestConfig
	for _, config := range configs {
		testConfigs = append(testConfigs, services.TestConfig{
			PayType:     config.PayType,
			Environment: environment,
			TestAmount:  0.01,
			UserID:      999999,
			ProductID:   1,
		})
	}

	// 创建测试服务
	testService := services.NewPaymentTestService(environment)

	// 执行测试
	results := testService.TestAllPayments(testConfigs)

	// 生成报告
	report := testService.GenerateTestReport(results)

	logger.Info("Test report generated",
		zap.String("environment", environment),
		zap.Int("total_tests", len(results)),
	)

	c.Data["json"] = utils.SuccessResponse(map[string]interface{}{
		"report":  report,
		"results": results,
	})
	c.ServeJSON()
}

// GetPaymentConfigs 获取支付配置列表
// @Title 获取支付配置列表
// @Description 获取所有支付方式的配置状态
// @Success 200 {object} utils.Response{data=[]models.PaymentConfig}
// @Security ApiKeyAuth
// @router /test/configs [get]
func (c *PaymentTestController) GetPaymentConfigs() {
	o := database.GetOrm()
	var configs []models.PaymentConfig
	_, err := o.QueryTable("payment_configs").All(&configs)
	if err != nil {
		logger.Error("Failed to get payment configs", zap.Error(err))
		c.Data["json"] = utils.ErrorResponse(http.StatusInternalServerError, "Failed to get payment configs")
		c.ServeJSON()
		return
	}

	// 隐藏敏感信息
	for i := range configs {
		configs[i].APIKey = "***"
		configs[i].AppSecret = "***"
	}

	c.Data["json"] = utils.SuccessResponse(configs)
	c.ServeJSON()
}

// UpdatePaymentConfig 更新支付配置
// @Title 更新支付配置
// @Description 更新指定支付方式的配置
// @Param id path int true "配置ID"
// @Param body body models.PaymentConfig true "配置信息"
// @Success 200 {object} utils.Response
// @Security ApiKeyAuth
// @router /test/configs/:id [put]
func (c *PaymentTestController) UpdatePaymentConfig() {
	idStr := c.Ctx.Input.Param(":id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid config ID")
		c.ServeJSON()
		return
	}

	var req models.PaymentConfig
	if err := c.ParseForm(&req); err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid request format")
		c.ServeJSON()
		return
	}

	o := database.GetOrm()

	// 查找配置
	var config models.PaymentConfig
	err = o.QueryTable("payment_configs").Filter("id", id).One(&config)
	if err != nil {
		logger.Error("Failed to get payment config", zap.Int("id", id), zap.Error(err))
		c.Data["json"] = utils.ErrorResponse(http.StatusNotFound, "Config not found")
		c.ServeJSON()
		return
	}

	// 更新配置
	config.AppID = req.AppID
	config.MchID = req.MchID
	config.NotifyURL = req.NotifyURL
	config.ReturnURL = req.ReturnURL
	config.Environment = req.Environment
	config.Status = req.Status

	// 只有提供了新密钥才更新
	if req.APIKey != "" && req.APIKey != "***" {
		config.APIKey = req.APIKey
	}
	if req.AppSecret != "" && req.AppSecret != "***" {
		config.AppSecret = req.AppSecret
	}

	_, err = o.Update(&config)
	if err != nil {
		logger.Error("Failed to update payment config", zap.Int("id", id), zap.Error(err))
		c.Data["json"] = utils.ErrorResponse(http.StatusInternalServerError, "Failed to update config")
		c.ServeJSON()
		return
	}

	logger.Info("Payment config updated successfully", zap.Int("id", id), zap.Int("pay_type", config.PayType))
	c.Data["json"] = utils.SuccessResponse(nil)
	c.ServeJSON()
}
