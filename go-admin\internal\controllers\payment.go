package controllers

import (
	"encoding/xml"
	"fmt"
	"io"
	"kakabs-admin/internal/database"
	"kakabs-admin/internal/logger"
	"kakabs-admin/internal/models"
	"kakabs-admin/internal/services"
	"kakabs-admin/internal/utils"
	"net/http"
	"strconv"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/server/web"
	"go.uber.org/zap"
)

// PaymentController 支付管理控制器
type PaymentController struct {
	web.Controller
}

// GetOrders 获取支付订单列表
// @Title 获取支付订单列表
// @Description 分页获取支付订单列表
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(20)
// @Param order_id query string false "订单号"
// @Param user_id query int false "用户ID"
// @Param status query int false "订单状态"
// @Success 200 {object} utils.PageResponse{data=[]models.PaymentOrder}
// @Security ApiKeyAuth
// @router /orders [get]
func (c *PaymentController) GetOrders() {
	var req models.PaymentOrderQuery
	if err := c.ParseForm(&req); err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid request parameters")
		c.ServeJSON()
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 20
	}

	o := database.GetOrm()
	qs := o.QueryTable("payment_orders")

	// 构建查询条件
	if req.OrderID != "" {
		qs = qs.Filter("order_id__icontains", req.OrderID)
	}
	if req.OutTradeNo != "" {
		qs = qs.Filter("out_trade_no__icontains", req.OutTradeNo)
	}
	if req.UserID > 0 {
		qs = qs.Filter("user_id", req.UserID)
	}
	if req.PayType > 0 {
		qs = qs.Filter("pay_type", req.PayType)
	}
	if req.Status >= 0 {
		qs = qs.Filter("status", req.Status)
	}
	if req.StartDate != "" {
		qs = qs.Filter("create_time__gte", req.StartDate)
	}
	if req.EndDate != "" {
		qs = qs.Filter("create_time__lte", req.EndDate)
	}

	// 获取总数
	total, err := qs.Count()
	if err != nil {
		logger.Error("Failed to count payment orders", zap.Error(err))
		c.Data["json"] = utils.DatabaseErrorResponse(err)
		c.ServeJSON()
		return
	}

	// 分页查询
	var orders []models.PaymentOrder
	offset := (req.Page - 1) * req.Size
	_, err = qs.OrderBy("-create_time").Limit(req.Size, offset).All(&orders)
	if err != nil {
		logger.Error("Failed to get payment orders", zap.Error(err))
		c.Data["json"] = utils.DatabaseErrorResponse(err)
		c.ServeJSON()
		return
	}

	c.Data["json"] = utils.PageSuccessResponse(orders, total, req.Page, req.Size)
	c.ServeJSON()
}

// GetOrder 获取支付订单详情
// @Title 获取支付订单详情
// @Description 根据ID获取支付订单详细信息
// @Param id path string true "订单ID"
// @Success 200 {object} utils.Response{data=models.PaymentOrder}
// @Failure 404 {object} utils.Response
// @Security ApiKeyAuth
// @router /orders/:id [get]
func (c *PaymentController) GetOrder() {
	orderID := c.Ctx.Input.Param(":id")
	if orderID == "" {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Order ID is required")
		c.ServeJSON()
		return
	}

	o := database.GetOrm()
	order := models.PaymentOrder{}
	
	err := o.QueryTable("payment_orders").Filter("order_id", orderID).One(&order)
	if err != nil {
		logger.Error("Failed to get payment order", zap.String("order_id", orderID), zap.Error(err))
		c.Data["json"] = utils.ResourceNotFoundResponse("Payment order")
		c.ServeJSON()
		return
	}

	c.Data["json"] = utils.SuccessResponse(order)
	c.ServeJSON()
}

// CreateWechatOrder 创建微信支付订单
// @Title 创建微信支付订单
// @Description 创建微信支付订单并返回支付参数
// @Param body body models.WechatPayRequest true "支付请求"
// @Success 200 {object} utils.Response{data=models.WechatPayResponse}
// @Security ApiKeyAuth
// @router /wechat/create [post]
func (c *PaymentController) CreateWechatOrder() {
	var req models.WechatPayRequest
	if err := c.ParseForm(&req); err != nil {
		c.Data["json"] = utils.ErrorResponse(http.StatusBadRequest, "Invalid request format")
		c.ServeJSON()
		return
	}

	// 获取客户端IP
	if req.ClientIP == "" {
		req.ClientIP = c.Ctx.Input.IP()
	}

	o := database.GetOrm()

	// 获取商品信息
	var product models.PaymentProduct
	err := o.QueryTable("payment_products").Filter("id", req.ProductID).Filter("status", 1).One(&product)
	if err != nil {
		logger.Error("Failed to get payment product", zap.Int("product_id", req.ProductID), zap.Error(err))
		c.Data["json"] = utils.ErrorResponse(http.StatusNotFound, "Product not found")
		c.ServeJSON()
		return
	}

	// 获取微信支付配置
	var config models.PaymentConfig
	err = o.QueryTable("payment_configs").Filter("pay_type", 1).Filter("status", 1).One(&config)
	if err != nil {
		logger.Error("Failed to get wechat pay config", zap.Error(err))
		c.Data["json"] = utils.ErrorResponse(http.StatusInternalServerError, "Payment config not found")
		c.ServeJSON()
		return
	}

	// 生成订单号
	orderID := c.generateOrderID(req.UserID)
	outTradeNo := c.generateOutTradeNo()

	// 创建支付订单
	order := models.PaymentOrder{
		OrderID:     orderID,
		OutTradeNo:  outTradeNo,
		UserID:      req.UserID,
		ProductID:   req.ProductID,
		ProductName: product.Name,
		PayType:     1, // 微信支付
		PayMethod:   req.PayMethod,
		TotalFee:    int(product.Price * 100), // 转换为分
		PayMoney:    product.Price,
		Currency:    product.Currency,
		Status:      0, // 待支付
		PayStatus:   0, // 未支付
		NotifyURL:   config.NotifyURL,
		ClientIP:    req.ClientIP,
		ExpireTime:  time.Now().Add(30 * time.Minute), // 30分钟过期
	}

	// 保存订单到数据库
	_, err = o.Insert(&order)
	if err != nil {
		logger.Error("Failed to create payment order", zap.Error(err))
		c.Data["json"] = utils.ErrorResponse(http.StatusInternalServerError, "Failed to create order")
		c.ServeJSON()
		return
	}

	// 创建微信支付服务
	wechatPay := services.NewWechatPayService(&config)

	// 创建支付订单
	payResp, err := wechatPay.CreateOrder(&order, &product)
	if err != nil {
		logger.Error("Failed to create wechat pay order", zap.Error(err))
		c.Data["json"] = utils.ErrorResponse(http.StatusInternalServerError, "Failed to create payment")
		c.ServeJSON()
		return
	}

	logger.Info("Wechat pay order created successfully",
		zap.String("order_id", orderID),
		zap.Int("user_id", req.UserID),
		zap.Int("product_id", req.ProductID),
	)

	c.Data["json"] = utils.SuccessResponse(payResp)
	c.ServeJSON()
}

// WechatNotify 微信支付回调
// @Title 微信支付回调
// @Description 处理微信支付回调通知
// @router /wechat/notify [post]
func (c *PaymentController) WechatNotify() {
	// 读取回调数据
	body, err := io.ReadAll(c.Ctx.Request.Body)
	if err != nil {
		logger.Error("Failed to read wechat notify body", zap.Error(err))
		c.Ctx.WriteString(`<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[读取数据失败]]></return_msg></xml>`)
		return
	}

	logger.Info("Received wechat pay notify", zap.String("body", string(body)))

	// 获取微信支付配置
	o := database.GetOrm()
	var config models.PaymentConfig
	err = o.QueryTable("payment_configs").Filter("pay_type", 1).Filter("status", 1).One(&config)
	if err != nil {
		logger.Error("Failed to get wechat pay config", zap.Error(err))
		c.Ctx.WriteString(`<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[配置错误]]></return_msg></xml>`)
		return
	}

	// 创建微信支付服务
	wechatPay := services.NewWechatPayService(&config)

	// 验证回调数据
	notifyData, err := wechatPay.VerifyNotify(body)
	if err != nil {
		logger.Error("Failed to verify wechat notify", zap.Error(err))
		c.Ctx.WriteString(`<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[验证失败]]></return_msg></xml>`)
		return
	}

	// 保存回调记录
	notifyRecord := models.PaymentNotify{
		OutTradeNo:    notifyData.OutTradeNo,
		TransactionID: notifyData.TransactionID,
		PayType:       1, // 微信支付
		NotifyType:    "payment",
		NotifyData:    string(body),
		ProcessStatus: 0, // 未处理
	}
	o.Insert(&notifyRecord)

	// 处理支付成功
	if notifyData.ReturnCode == "SUCCESS" && notifyData.ResultCode == "SUCCESS" {
		err = c.handlePaymentSuccess(notifyData)
		if err != nil {
			logger.Error("Failed to handle payment success", zap.Error(err))
			// 更新处理状态为失败
			notifyRecord.ProcessStatus = 2
			notifyRecord.ProcessResult = err.Error()
			notifyRecord.ProcessTime = time.Now()
			o.Update(&notifyRecord, "process_status", "process_result", "process_time")
			
			c.Ctx.WriteString(`<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[处理失败]]></return_msg></xml>`)
			return
		}

		// 更新处理状态为成功
		notifyRecord.ProcessStatus = 1
		notifyRecord.ProcessResult = "success"
		notifyRecord.ProcessTime = time.Now()
		o.Update(&notifyRecord, "process_status", "process_result", "process_time")
	}

	// 返回成功响应
	c.Ctx.WriteString(`<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>`)
}

// handlePaymentSuccess 处理支付成功
func (c *PaymentController) handlePaymentSuccess(notifyData *services.PayNotifyData) error {
	return database.Transaction(func(o orm.Ormer) error {
		// 查找订单
		var order models.PaymentOrder
		err := o.QueryTable("payment_orders").Filter("out_trade_no", notifyData.OutTradeNo).One(&order)
		if err != nil {
			return fmt.Errorf("order not found: %s", notifyData.OutTradeNo)
		}

		// 检查订单状态
		if order.Status == 1 {
			logger.Info("Order already paid", zap.String("out_trade_no", notifyData.OutTradeNo))
			return nil // 订单已支付，直接返回成功
		}

		// 更新订单状态
		order.Status = 1 // 已支付
		order.PayStatus = 1 // 支付成功
		order.TransactionID = notifyData.TransactionID
		order.PayTime = time.Now()
		
		_, err = o.Update(&order, "status", "pay_status", "transaction_id", "pay_time", "update_time")
		if err != nil {
			return fmt.Errorf("failed to update order: %w", err)
		}

		// 获取商品信息
		var product models.PaymentProduct
		err = o.QueryTable("payment_products").Filter("id", order.ProductID).One(&product)
		if err != nil {
			return fmt.Errorf("failed to get product: %w", err)
		}

		// 发放道具给用户
		err = c.grantItemsToUser(o, order.UserID, product.ItemType, product.ItemCount+product.BonusCount, "充值获得")
		if err != nil {
			return fmt.Errorf("failed to grant items: %w", err)
		}

		logger.Info("Payment processed successfully",
			zap.String("out_trade_no", notifyData.OutTradeNo),
			zap.String("transaction_id", notifyData.TransactionID),
			zap.Int("user_id", order.UserID),
			zap.Int64("item_count", product.ItemCount+product.BonusCount),
		)

		return nil
	})
}

// grantItemsToUser 发放道具给用户
func (c *PaymentController) grantItemsToUser(o orm.Ormer, userID int, itemType int, itemCount int64, reason string) error {
	// 获取用户信息
	var user models.User
	err := o.QueryTable("users").Filter("user_id", userID).One(&user)
	if err != nil {
		return fmt.Errorf("user not found: %d", userID)
	}

	// 根据道具类型更新用户资产
	switch itemType {
	case 1: // 金豆
		oldGold := user.Gold
		user.Gold += itemCount
		_, err = o.Update(&user, "gold", "update_time")
		if err != nil {
			return err
		}

		// 记录金豆日志
		goldLog := models.UserGoldLog{
			UserID:     userID,
			Type:       1, // 收入
			Amount:     itemCount,
			Before:     oldGold,
			After:      user.Gold,
			Reason:     3, // 充值获得
			ReasonDesc: reason,
		}
		_, err = o.Insert(&goldLog)
		return err

	case 2: // 钻石
		oldDiamond := user.Diamond
		user.Diamond += itemCount
		_, err = o.Update(&user, "diamond", "update_time")
		if err != nil {
			return err
		}

		// 记录钻石日志
		diamondLog := models.UserDiamondLog{
			UserID:     userID,
			Type:       1, // 收入
			Amount:     itemCount,
			Before:     oldDiamond,
			After:      user.Diamond,
			Reason:     3, // 充值获得
			ReasonDesc: reason,
		}
		_, err = o.Insert(&diamondLog)
		return err

	case 3: // 门票
		user.Ticket += int(itemCount)
		_, err = o.Update(&user, "ticket", "update_time")
		return err

	case 5: // 福卡
		user.FuCard += int(itemCount)
		_, err = o.Update(&user, "fu_card", "update_time")
		return err

	default:
		return fmt.Errorf("unsupported item type: %d", itemType)
	}
}

// generateOrderID 生成订单号
func (c *PaymentController) generateOrderID(userID int) string {
	return fmt.Sprintf("ORD%d%d", time.Now().Unix(), userID)
}

// generateOutTradeNo 生成商户订单号
func (c *PaymentController) generateOutTradeNo() string {
	return fmt.Sprintf("PAY%d%04d", time.Now().Unix(), time.Now().Nanosecond()%10000)
}
