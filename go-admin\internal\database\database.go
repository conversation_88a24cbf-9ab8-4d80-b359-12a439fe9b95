package database

import (
	"fmt"
	"kakabs-admin/internal/config"
	"kakabs-admin/internal/logger"
	"time"

	"github.com/beego/beego/v2/client/orm"
	_ "github.com/go-sql-driver/mysql"
	"go.uber.org/zap"
)

// 数据库连接名称常量
const (
	DefaultDB = "default"
	AdminDB   = "admin"
	ConfigDB  = "config"
	WarbandDB = "warband"
)

// Init 初始化数据库连接
func Init() error {
	// 注册数据库驱动
	orm.RegisterDriver("mysql", orm.DRMySQL)

	// 初始化各个数据库连接
	databases := []string{DefaultDB, AdminDB, ConfigDB, WarbandDB}
	
	for _, dbName := range databases {
		if err := initDatabase(dbName); err != nil {
			return fmt.Errorf("failed to init database %s: %w", dbName, err)
		}
	}

	// 设置默认数据库
	orm.SetDefaultDB(DefaultDB)

	// 开发模式下打印SQL
	if config.GetString("app.mode") == "dev" {
		orm.Debug = true
	}

	return nil
}

// initDatabase 初始化单个数据库连接
func initDatabase(name string) error {
	dbConfig, err := config.GetDatabaseConfig(name)
	if err != nil {
		return err
	}

	// 构建DSN
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=true&loc=Local",
		dbConfig.Username,
		dbConfig.Password,
		dbConfig.Host,
		dbConfig.Port,
		dbConfig.Database,
		dbConfig.Charset,
	)

	// 注册数据库
	if err := orm.RegisterDataBase(name, "mysql", dsn); err != nil {
		return fmt.Errorf("failed to register database %s: %w", name, err)
	}

	// 设置连接池参数
	db, err := orm.GetDB(name)
	if err != nil {
		return fmt.Errorf("failed to get database %s: %w", name, err)
	}

	db.SetMaxIdleConns(dbConfig.MaxIdleConns)
	db.SetMaxOpenConns(dbConfig.MaxOpenConns)
	db.SetConnMaxLifetime(time.Duration(dbConfig.ConnMaxLifetime) * time.Second)

	// 测试连接
	if err := db.Ping(); err != nil {
		return fmt.Errorf("failed to ping database %s: %w", name, err)
	}

	logger.Info("Database connected successfully",
		zap.String("name", name),
		zap.String("host", dbConfig.Host),
		zap.String("database", dbConfig.Database),
	)

	return nil
}

// GetOrm 获取ORM实例
func GetOrm(alias ...string) orm.Ormer {
	if len(alias) > 0 {
		return orm.NewOrmUsingDB(alias[0])
	}
	return orm.NewOrm()
}

// GetAdminOrm 获取管理数据库ORM实例
func GetAdminOrm() orm.Ormer {
	return orm.NewOrmUsingDB(AdminDB)
}

// GetConfigOrm 获取配置数据库ORM实例
func GetConfigOrm() orm.Ormer {
	return orm.NewOrmUsingDB(ConfigDB)
}

// GetWarbandOrm 获取战队数据库ORM实例
func GetWarbandOrm() orm.Ormer {
	return orm.NewOrmUsingDB(WarbandDB)
}

// Transaction 执行事务
func Transaction(fn func(orm.Ormer) error, alias ...string) error {
	o := GetOrm(alias...)
	
	if err := o.Begin(); err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	defer func() {
		if r := recover(); r != nil {
			o.Rollback()
			panic(r)
		}
	}()

	if err := fn(o); err != nil {
		o.Rollback()
		return err
	}

	if err := o.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// Close 关闭数据库连接
func Close() {
	// BeeGo ORM会自动管理连接池，通常不需要手动关闭
	logger.Info("Database connections closed")
}
