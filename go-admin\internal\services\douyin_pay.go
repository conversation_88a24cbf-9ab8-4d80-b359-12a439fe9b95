package services

import (
	"bytes"
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"io"
	"kakabs-admin/internal/logger"
	"kakabs-admin/internal/models"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
)

// DouyinPayService 抖音支付服务
type DouyinPayService struct {
	AppID       string
	MerchantID  string
	PrivateKey  string
	PublicKey   string
	NotifyURL   string
	Environment string // sandbox/prod
}

// NewDouyinPayService 创建抖音支付服务实例
func NewDouyinPayService(config *models.PaymentConfig) *DouyinPayService {
	return &DouyinPayService{
		AppID:       config.AppID,
		MerchantID:  config.MchID,
		PrivateKey:  config.APIKey,
		PublicKey:   config.AppSecret,
		NotifyURL:   config.NotifyURL,
		Environment: config.Environment,
	}
}

// DouyinPayRequest 抖音支付请求结构
type DouyinPayRequest struct {
	AppID       string `json:"app_id"`
	MerchantID  string `json:"merchant_id"`
	OutOrderNo  string `json:"out_order_no"`
	TotalAmount int    `json:"total_amount"`
	Subject     string `json:"subject"`
	Body        string `json:"body"`
	ValidTime   int    `json:"valid_time"`
	NotifyURL   string `json:"notify_url"`
	ThirdpartyID string `json:"thirdparty_id,omitempty"`
	DisableMsg   int    `json:"disable_msg"`
	MsgPage      string `json:"msg_page,omitempty"`
	StoreUID     string `json:"store_uid,omitempty"`
	Timestamp   int64  `json:"timestamp"`
	Sign        string `json:"sign"`
}

// DouyinPayResponse 抖音支付响应结构
type DouyinPayResponse struct {
	ErrNo   int    `json:"err_no"`
	ErrTips string `json:"err_tips"`
	Data    struct {
		OrderID    string `json:"order_id"`
		OrderToken string `json:"order_token"`
	} `json:"data"`
}

// DouyinNotifyData 抖音支付回调数据
type DouyinNotifyData struct {
	Timestamp    string `json:"timestamp"`
	Nonce        string `json:"nonce"`
	Msg          string `json:"msg"`
	Type         string `json:"type"`
	MsgSignature string `json:"msg_signature"`
}

// DouyinPaymentData 支付数据
type DouyinPaymentData struct {
	AppID       string `json:"appid"`
	OrderID     string `json:"order_id"`
	OrderToken  string `json:"order_token"`
	OutOrderNo  string `json:"out_order_no"`
	TotalAmount int    `json:"total_amount"`
	PayTime     string `json:"pay_time"`
	Way         string `json:"way"`
	ChannelNo   string `json:"channel_no"`
	ChannelGatewayNo string `json:"channel_gateway_no"`
	SellerUID   string `json:"seller_uid"`
	ItemID      string `json:"item_id"`
	CpExtra     string `json:"cp_extra"`
}

// CreateOrder 创建抖音支付订单
func (d *DouyinPayService) CreateOrder(order *models.PaymentOrder, product *models.PaymentProduct) (*models.WechatPayResponse, error) {
	// 构建请求参数
	req := &DouyinPayRequest{
		AppID:       d.AppID,
		MerchantID:  d.MerchantID,
		OutOrderNo:  order.OutTradeNo,
		TotalAmount: order.TotalFee, // 抖音支付使用分为单位
		Subject:     product.Name,
		Body:        product.Description,
		ValidTime:   1800, // 30分钟有效期
		NotifyURL:   d.NotifyURL,
		DisableMsg:  0, // 允许推送消息
		Timestamp:   time.Now().Unix(),
	}

	// 生成签名
	sign, err := d.generateSign(req)
	if err != nil {
		return nil, fmt.Errorf("generate sign failed: %w", err)
	}
	req.Sign = sign

	// 发送请求
	resp, err := d.sendCreateOrderRequest(req)
	if err != nil {
		return nil, fmt.Errorf("send request failed: %w", err)
	}

	if resp.ErrNo != 0 {
		return nil, fmt.Errorf("create order failed: %s", resp.ErrTips)
	}

	// 构建支付参数
	payData := map[string]interface{}{
		"order_id":    resp.Data.OrderID,
		"order_token": resp.Data.OrderToken,
		"app_id":      d.AppID,
	}

	return &models.WechatPayResponse{
		OrderID:    order.OrderID,
		OutTradeNo: order.OutTradeNo,
		PayData:    payData,
	}, nil
}

// sendCreateOrderRequest 发送创建订单请求
func (d *DouyinPayService) sendCreateOrderRequest(req *DouyinPayRequest) (*DouyinPayResponse, error) {
	// 选择API地址
	apiURL := "https://developer.toutiao.com/api/apps/ecpay/v1/create_order"
	if d.Environment == "sandbox" {
		apiURL = "https://open-sandbox.douyin.com/api/apps/ecpay/v1/create_order"
	}

	// 序列化请求数据
	requestData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("marshal request failed: %w", err)
	}

	// 创建HTTP请求
	httpReq, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(requestData))
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("send request failed: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response failed: %w", err)
	}

	// 解析响应
	var payResp DouyinPayResponse
	err = json.Unmarshal(responseData, &payResp)
	if err != nil {
		return nil, fmt.Errorf("unmarshal response failed: %w", err)
	}

	logger.Debug("Douyin pay create order response",
		zap.String("response", string(responseData)),
		zap.Int("err_no", payResp.ErrNo),
	)

	return &payResp, nil
}

// VerifyNotify 验证抖音支付回调
func (d *DouyinPayService) VerifyNotify(notifyData *DouyinNotifyData) (*DouyinPaymentData, error) {
	// 验证签名
	if !d.verifyNotifySign(notifyData) {
		return nil, fmt.Errorf("verify notify sign failed")
	}

	// 解析支付数据
	var paymentData DouyinPaymentData
	err := json.Unmarshal([]byte(notifyData.Msg), &paymentData)
	if err != nil {
		return nil, fmt.Errorf("unmarshal payment data failed: %w", err)
	}

	return &paymentData, nil
}

// generateSign 生成签名
func (d *DouyinPayService) generateSign(req *DouyinPayRequest) (string, error) {
	// 构建参数map
	params := map[string]string{
		"app_id":       req.AppID,
		"merchant_id":  req.MerchantID,
		"out_order_no": req.OutOrderNo,
		"total_amount": strconv.Itoa(req.TotalAmount),
		"subject":      req.Subject,
		"body":         req.Body,
		"valid_time":   strconv.Itoa(req.ValidTime),
		"notify_url":   req.NotifyURL,
		"disable_msg":  strconv.Itoa(req.DisableMsg),
		"timestamp":    strconv.FormatInt(req.Timestamp, 10),
	}

	// 添加可选参数
	if req.ThirdpartyID != "" {
		params["thirdparty_id"] = req.ThirdpartyID
	}
	if req.MsgPage != "" {
		params["msg_page"] = req.MsgPage
	}
	if req.StoreUID != "" {
		params["store_uid"] = req.StoreUID
	}

	// 构建待签名字符串
	signStr := d.buildSignString(params)

	// RSA签名
	return d.rsaSign(signStr)
}

// buildSignString 构建待签名字符串
func (d *DouyinPayService) buildSignString(params map[string]string) string {
	var keys []string
	for k := range params {
		if params[k] != "" {
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	var signParts []string
	for _, k := range keys {
		signParts = append(signParts, k+"="+params[k])
	}

	return strings.Join(signParts, "&")
}

// rsaSign RSA签名
func (d *DouyinPayService) rsaSign(signStr string) (string, error) {
	// 解析私钥
	block, _ := pem.Decode([]byte(d.PrivateKey))
	if block == nil {
		return "", fmt.Errorf("private key decode failed")
	}

	privateKey, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return "", fmt.Errorf("parse private key failed: %w", err)
	}

	rsaPrivateKey, ok := privateKey.(*rsa.PrivateKey)
	if !ok {
		return "", fmt.Errorf("private key is not RSA key")
	}

	// SHA256哈希
	hash := sha256.Sum256([]byte(signStr))

	// RSA签名
	signature, err := rsa.SignPKCS1v15(rand.Reader, rsaPrivateKey, crypto.SHA256, hash[:])
	if err != nil {
		return "", fmt.Errorf("rsa sign failed: %w", err)
	}

	return base64.StdEncoding.EncodeToString(signature), nil
}

// verifyNotifySign 验证回调签名
func (d *DouyinPayService) verifyNotifySign(notifyData *DouyinNotifyData) bool {
	// 构建待验签字符串
	signStr := notifyData.Timestamp + notifyData.Nonce + notifyData.Msg

	// 解析公钥
	block, _ := pem.Decode([]byte(d.PublicKey))
	if block == nil {
		logger.Error("Public key decode failed")
		return false
	}

	publicKey, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		logger.Error("Parse public key failed", zap.Error(err))
		return false
	}

	rsaPublicKey, ok := publicKey.(*rsa.PublicKey)
	if !ok {
		logger.Error("Public key is not RSA key")
		return false
	}

	// 解码签名
	signature, err := base64.StdEncoding.DecodeString(notifyData.MsgSignature)
	if err != nil {
		logger.Error("Decode signature failed", zap.Error(err))
		return false
	}

	// SHA256哈希
	hash := sha256.Sum256([]byte(signStr))

	// 验证签名
	err = rsa.VerifyPKCS1v15(rsaPublicKey, crypto.SHA256, hash[:], signature)
	return err == nil
}

// QueryOrder 查询订单状态
func (d *DouyinPayService) QueryOrder(outOrderNo string) (*DouyinPaymentData, error) {
	// 构建查询参数
	params := map[string]string{
		"app_id":       d.AppID,
		"merchant_id":  d.MerchantID,
		"out_order_no": outOrderNo,
		"timestamp":    strconv.FormatInt(time.Now().Unix(), 10),
	}

	// 生成签名
	signStr := d.buildSignString(params)
	sign, err := d.rsaSign(signStr)
	if err != nil {
		return nil, fmt.Errorf("generate sign failed: %w", err)
	}
	params["sign"] = sign

	// 选择API地址
	apiURL := "https://developer.toutiao.com/api/apps/ecpay/v1/query_order"
	if d.Environment == "sandbox" {
		apiURL = "https://open-sandbox.douyin.com/api/apps/ecpay/v1/query_order"
	}

	// 构建请求URL
	var queryParts []string
	for k, v := range params {
		queryParts = append(queryParts, k+"="+v)
	}
	queryURL := apiURL + "?" + strings.Join(queryParts, "&")

	// 发送GET请求
	resp, err := http.Get(queryURL)
	if err != nil {
		return nil, fmt.Errorf("send query request failed: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response failed: %w", err)
	}

	// 解析响应
	var queryResp struct {
		ErrNo   int    `json:"err_no"`
		ErrTips string `json:"err_tips"`
		Data    DouyinPaymentData `json:"data"`
	}

	err = json.Unmarshal(responseData, &queryResp)
	if err != nil {
		return nil, fmt.Errorf("unmarshal response failed: %w", err)
	}

	if queryResp.ErrNo != 0 {
		return nil, fmt.Errorf("query order failed: %s", queryResp.ErrTips)
	}

	return &queryResp.Data, nil
}
